#!/usr/bin/env python3
"""
Main entry point for the Sports Odds Scraper application.
This file serves as an enhanced wrapper that runs both scraping and event matching.
"""

import os
import sys
# import json - removed as no longer needed
import argparse
import datetime
import asyncio
import time
from typing import List, Dict, Any
from concurrent.futures import ThreadPoolExecutor, as_completed
from collections import defaultdict

from scrapers import ScraperFactory
# Removed unused imports
from utils import FileHandler
from models import SportEvent, EventCollection
from scripts.match_events import (
    load_events_from_directory, match_events,
    save_matched_events, save_comparison_table, save_comparison_excel
)
from scripts.scrape_odds import scrape_odds_for_matched_events
# Removed arbitrage analyzer import

def run_scraper(bookie_name: str, sport: str, output_dir: str, get_detailed_odds: bool = False,
              use_async: bool = False) -> Dict[str, Any]:
    """
    Run a scraper for a specific bookmaker and sport

    Args:
        bookie_name: Name of the bookmaker
        sport: Sport to scrape
        output_dir: Directory to save output files
        get_detailed_odds: Whether to get detailed odds
        use_async: Whether to use async scraper if available

    Returns:
        Dictionary with results
    """
    try:
        # Try to use async scraper if requested and available
        if use_async and bookie_name in ScraperFactory.get_available_async_bookmakers():
            print(f"Using async scraper for {bookie_name}")
            scraper = ScraperFactory.get_async_scraper(bookie_name, sport, output_dir)
            return asyncio.run(scraper.run(get_detailed_odds))
        else:
            # Fall back to synchronous scraper
            scraper = ScraperFactory.get_scraper(bookie_name, sport, output_dir)
            return scraper.run(get_detailed_odds)
    except Exception as e:
        print(f"Error running scraper for {bookie_name}: {e}")
        return {
            "bookie": bookie_name,
            "sport": sport,
            "error": str(e),
            "events_count": 0,
            "events_data": []
        }


def run_scrapers(bookmakers: List[str], sport: str, output_dir: str, get_detailed_odds: bool = False,
               parallel: bool = True, use_async: bool = False) -> Dict[str, Dict[str, Any]]:
    """
    Run scrapers for multiple bookmakers

    Args:
        bookmakers: List of bookmaker names
        sport: Sport to scrape
        output_dir: Directory to save output files
        get_detailed_odds: Whether to get detailed odds
        parallel: Whether to run scrapers in parallel
        use_async: Whether to use async scrapers if available

    Returns:
        Dictionary with results for each bookmaker
    """
    results = {}

    # Split bookmakers into async-capable and regular
    async_bookmakers = []
    sync_bookmakers = []

    if use_async:
        available_async_bookmakers = ScraperFactory.get_available_async_bookmakers()
        for bookie in bookmakers:
            if bookie in available_async_bookmakers:
                async_bookmakers.append(bookie)
            else:
                sync_bookmakers.append(bookie)
    else:
        sync_bookmakers = bookmakers

    # Process synchronous bookmakers
    if sync_bookmakers:
        if parallel:
            # Run sync scrapers in parallel
            with ThreadPoolExecutor() as executor:
                future_to_bookie = {
                    executor.submit(run_scraper, bookie, sport, output_dir, get_detailed_odds, False): bookie
                    for bookie in sync_bookmakers
                }

                for future in as_completed(future_to_bookie):
                    bookie = future_to_bookie[future]
                    try:
                        results[bookie] = future.result()
                    except Exception as e:
                        print(f"Error running scraper for {bookie}: {e}")
                        results[bookie] = {
                            "bookie": bookie,
                            "sport": sport,
                            "error": str(e),
                            "events_count": 0,
                            "events_data": []
                        }
        else:
            # Run sync scrapers sequentially
            for bookie in sync_bookmakers:
                results[bookie] = run_scraper(bookie, sport, output_dir, get_detailed_odds, False)

    # Process asynchronous bookmakers - always run these one by one
    # as they already have internal parallelism
    for bookie in async_bookmakers:
        print(f"Running async scraper for {bookie}...")
        try:
            results[bookie] = run_scraper(bookie, sport, output_dir, get_detailed_odds, True)
        except Exception as e:
            print(f"Error running async scraper for {bookie}: {e}")
            results[bookie] = {
                "bookie": bookie,
                "sport": sport,
                "error": str(e),
                "events_count": 0,
                "events_data": []
            }

    return results


def merge_results(results: Dict[str, Dict[str, Any]], output_dir: str, get_detailed_odds: bool = False) -> Dict[str, Any]:
    """
    Merge results from multiple bookmakers

    Args:
        results: Dictionary with results for each bookmaker
        output_dir: Directory to save merged results
        get_detailed_odds: Whether to include detailed odds

    Returns:
        Dictionary with merged results
    """
    # Ensure output directory exists
    FileHandler.ensure_directory(output_dir)

    # Get current date
    today = datetime.datetime.now().strftime("%Y-%m-%d")

    # Create event collections
    events_collection = EventCollection()
    odds_collection = EventCollection()

    # Merge events from all bookmakers
    for _, result in results.items():
        if "error" in result:
            continue

        for event_data in result["events_data"]:
            event = SportEvent.from_dict(event_data)
            events_collection.add_event(event)

        if get_detailed_odds and "odds_data" in result:
            for odds_data in result["odds_data"]:
                event = SportEvent.from_dict(odds_data)
                odds_collection.add_event(event)

    # Save merged results
    events_path = os.path.join(output_dir, f"merged_{today}_events.json")
    FileHandler.save_json(events_collection.to_dict_list(), events_path)

    merged_results = {
        "date": today,
        "bookmakers": list(results.keys()),
        "events_count": len(events_collection.events),
        "events_path": events_path
    }

    if get_detailed_odds:
        odds_path = os.path.join(output_dir, f"merged_{today}_odds.json")
        FileHandler.save_json(odds_collection.to_dict_list(), odds_path)
        merged_results["odds_count"] = len(odds_collection.events)
        merged_results["odds_path"] = odds_path

    return merged_results


# Removed arbitrage analysis function


def main():
    """Enhanced main entry point that runs both scraping and matching"""
    # Parse command line arguments
    parser = argparse.ArgumentParser(description='Sports Odds Scraper with Event Matching')
    parser.add_argument('--bookmakers', '-b', nargs='+', help='Bookmakers to scrape (default: all)')
    parser.add_argument('--sport', '-s', default='football', help='Sport to scrape (default: football)')
    # Removed detailed odds option
    parser.add_argument('--sequential', action='store_true', help='Run scrapers sequentially (not in parallel)')
    parser.add_argument('--output-dir', '-o', default='output', help='Output directory (default: output)')
    parser.add_argument('--matched-dir', '-m', default='matched', help='Directory for matched events (default: matched)')
    # Removed arbitrage analysis argument
    parser.add_argument('--debug', action='store_true', help='Show debug information')
    parser.add_argument('--no-async', dest='use_async', action='store_false', default=True,
                        help='Disable async scrapers (use synchronous scrapers only)')
    parser.add_argument('--skip-scraping', action='store_true', help='Skip scraping and only match existing events')
    parser.add_argument('--skip-matching', action='store_true', help='Skip event matching')
    parser.add_argument('--clean', action='store_true', help='Clean output directory before scraping')
    parser.add_argument('--scrape-odds', action='store_true', help='Scrape detailed odds for matched events')
    parser.add_argument('--name-similarity', '-n', type=float, default=0.85,
                        help='Name similarity threshold for matching (default: 0.85)')
    parser.add_argument('--team-similarity', '-t', type=float, default=0.8,
                        help='Team similarity threshold for matching (default: 0.8)')
    parser.add_argument('--time-window', '-w', type=int, default=120,
                        help='Time window in minutes for matching (default: 120)')
    # Removed analyze-only argument
    args = parser.parse_args()

    # Ensure output directories exist
    os.makedirs(args.output_dir, exist_ok=True)
    os.makedirs(args.matched_dir, exist_ok=True)

    # Clean output directory if requested
    if args.clean and not args.skip_scraping:
        print("Cleaning output directory...")
        for bookmaker in ScraperFactory.get_available_bookmakers():
            bookmaker_dir = os.path.join(args.output_dir, bookmaker, args.sport)
            if os.path.exists(bookmaker_dir):
                for file in os.listdir(bookmaker_dir):
                    file_path = os.path.join(bookmaker_dir, file)
                    if os.path.isfile(file_path):
                        os.remove(file_path)
                        print(f"Removed {file_path}")

    # Removed analyze-only mode

    # Step 1: Run the scrapers
    if not args.skip_scraping:
        print("\n=== STEP 1: SCRAPING EVENTS ===\n")

        # Get available bookmakers
        available_bookmakers = ScraperFactory.get_available_bookmakers()

        # Determine which bookmakers to scrape
        bookmakers = args.bookmakers if args.bookmakers else available_bookmakers

        # Validate bookmakers
        for bookie in bookmakers[:]:  # Make a copy to avoid modifying during iteration
            if bookie not in available_bookmakers:
                print(f"Warning: Unknown bookmaker '{bookie}'. Available bookmakers: {', '.join(available_bookmakers)}")
                bookmakers.remove(bookie)

        if not bookmakers:
            print("No valid bookmakers specified.")
            return 1

        # Run scrapers
        print(f"Running scrapers for bookmakers: {', '.join(bookmakers)}")

        # Show which bookmakers support async mode
        async_bookmakers = ScraperFactory.get_available_async_bookmakers()
        async_supported = [b for b in bookmakers if b in async_bookmakers]

        if args.use_async:
            if async_supported:
                print(f"Using async scrapers for: {', '.join(async_supported)}")
                if len(async_supported) < len(bookmakers):
                    sync_only = [b for b in bookmakers if b not in async_bookmakers]
                    print(f"Using regular scrapers for: {', '.join(sync_only)}")
            else:
                print("No bookmakers in selection support async mode, using regular scrapers")
        else:
            if async_supported:
                print("Async mode disabled. Use --async to enable faster scraping.")

        # Run the scrapers
        results = run_scrapers(
            bookmakers,
            args.sport,
            args.output_dir,
            False,  # No detailed odds by default
            not args.sequential,
            args.use_async
        )

        # Merge and display results
        merged_results = merge_results(results, args.output_dir, False)

        print(f"Events scraped: {merged_results['events_count']}")
        print(f"Events saved to: {merged_results['events_path']}")

        # Check if detailed odds were scraped
        if 'odds_count' in merged_results:
            print(f"Detailed odds scraped: {merged_results['odds_count']}")
            print(f"Detailed odds saved to: {merged_results['odds_path']}")

        # Removed arbitrage analysis

        print("\nScraping completed!")

    # Step 2: Match events across bookmakers
    if not args.skip_matching:
        print("\n=== STEP 2: MATCHING EVENTS ACROSS BOOKMAKERS ===\n")

        # Load events with progress bar
        events_by_bookmaker = load_events_from_directory(args.output_dir)

        if not events_by_bookmaker:
            print("No events loaded. Exiting.")
            return 1

        total_events = sum(len(events) for events in events_by_bookmaker.values())
        print(f"Loaded {total_events} events from {len(events_by_bookmaker)} bookmakers")

        # Match events with detailed timing
        print("\nMatching events across bookmakers...")
        print(f"Using parameters: name_similarity={args.name_similarity:.2f}, team_similarity={args.team_similarity:.2f}, time_window={args.time_window} minutes")

        # Time the operation
        match_start_time = time.time()

        # Create the matcher with our optimized implementation and run matching
        matcher = match_events(
            events_by_bookmaker,
            name_similarity=args.name_similarity,
            team_similarity=args.team_similarity,
            time_window=args.time_window
        )

        # Calculate statistics for reporting
        total_matches = len(matcher.matched_events)
        multi_bookmaker_matches = sum(1 for event in matcher.matched_events if len(event.bookmaker_events) > 1)
        match_duration = time.time() - match_start_time

        # Display summary statistics
        print(f"\n=== Matching Summary ===")
        print(f"Found {total_matches} total matched events in {match_duration:.2f} seconds")
        print(f"Found {multi_bookmaker_matches} events matched across multiple bookmakers ({(multi_bookmaker_matches/total_matches)*100:.1f}% of total)")

        # Print bookmaker distribution
        bookmaker_count = defaultdict(int)
        bookmaker_shared = defaultdict(int)
        for event in matcher.matched_events:
            bookies = event.get_bookmakers()
            for bookmaker in bookies:
                bookmaker_count[bookmaker] += 1
                if len(bookies) > 1:
                    bookmaker_shared[bookmaker] += 1

        print("\nEvents per bookmaker:")
        print(f"{'Bookmaker':<15} {'Total Events':<15} {'Shared Events':<15} {'% Shared':<10}")
        print(f"{'-'*15} {'-'*15} {'-'*15} {'-'*10}")
        for bookmaker, count in sorted(bookmaker_count.items(), key=lambda x: x[1], reverse=True):
            shared = bookmaker_shared[bookmaker]
            percent = (shared / count) * 100 if count > 0 else 0
            print(f"{bookmaker:<15} {count:<15} {shared:<15} {percent:.1f}%")

        # Print bookmaker matching combinations
        if multi_bookmaker_matches > 0:
            # Get top combinations
            bookmaker_pairs = defaultdict(int)
            for event in matcher.matched_events:
                bookies = event.get_bookmakers()
                if len(bookies) > 1:
                    # Create pairs of bookmakers
                    for i, b1 in enumerate(bookies):
                        for b2 in bookies[i+1:]:
                            pair = tuple(sorted([b1, b2]))
                            bookmaker_pairs[pair] += 1

            # Print top 5 most common bookmaker pairs
            print("\nTop 5 bookmaker combinations:")
            for (b1, b2), count in sorted(bookmaker_pairs.items(), key=lambda x: x[1], reverse=True)[:5]:
                print(f"- {b1} + {b2}: {count} shared events")

        # Count events by number of bookmakers
        bookie_counts = defaultdict(int)
        for event in matcher.matched_events:
            bookie_counts[len(event.bookmaker_events)] += 1

        print("\nEvents by number of bookmakers:")
        for count, num_events in sorted(bookie_counts.items()):
            percent = (num_events / total_matches) * 100
            print(f"- {count} bookmakers: {num_events} events ({percent:.1f}%)")

        # Generate timestamp for filenames
        timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")

        # Save matched events to JSON
        json_path = os.path.join(args.matched_dir, f"matched_events_{timestamp}.json")
        save_matched_events(matcher, json_path)

        # Save comparison table to CSV
        csv_path = os.path.join(args.matched_dir, f"comparison_table_{timestamp}.csv")
        save_comparison_table(matcher, csv_path)

        # Save comparison table to Excel with formatting
        excel_path = os.path.join(args.matched_dir, f"comparison_table_{timestamp}.xlsx")
        save_comparison_excel(matcher, excel_path)

        # Scrape detailed odds for matched events if requested
        if args.scrape_odds:
            # Get all available bookmakers if none specified
            bookmakers = args.bookmakers or ScraperFactory.get_available_bookmakers()
            odds_results = scrape_odds_for_matched_events(
                matcher,
                bookmakers,
                args.sport,
                args.output_dir,
                args.use_async
            )
            print(f"Detailed odds scraped: {odds_results['odds_count']}")
            print(f"Detailed odds saved to: {odds_results['odds_path']}")

        print("\nEvent matching complete!")

    # Display completion message
    print("\nAll tasks completed successfully!")
    return 0

if __name__ == "__main__":
    sys.exit(main())