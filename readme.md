# Sports Odds Scraper

A flexible and extensible framework for scraping sports betting odds from various bookmakers.

## Features

- Scrape sports betting odds from multiple bookmakers
- Support for basic event metadata and detailed odds
- Parallel scraping for improved performance
- Easy to extend with new bookmakers and sports
- Command-line interface for flexible usage
- Asynchronous scraping for improved performance (2-5x faster)
- Advanced event matching across bookmakers using string similarity
- Comparison tables with normalized event data and odds
- Clean, modular architecture for easy maintenance

## Currently Supported Bookmakers

- Bet777
- Betcenter
- Betfirst
- Napoleon
- Pinnacle
- Unibet

## Installation

1. Clone the repository:
```bash
git clone https://github.com/yourusername/sports-odds-scraper.git
cd sports-odds-scraper
```

2. Install the required dependencies:
```bash
pip install -r requirements.txt
```

## Usage

### Command Line Interface

The application now combines both scraping and event matching in a streamlined process.

#### Combined Scraping and Matching

Run the application with a single command that handles both scraping and event matching:

```bash
python main.py [OPTIONS]
```

Options:
- `--bookmakers`, `-b`: Specify bookmakers to scrape (default: all available)
- `--sport`, `-s`: Sport to scrape (default: football)
- `--detailed-odds`, `-d`: Get detailed odds in addition to basic event data
- `--sequential`: Run scrapers sequentially (not in parallel)
- `--output-dir`, `-o`: Directory for scraper output (default: output)
- `--matched-dir`, `-m`: Directory for matched events (default: matched)
- `--no-async`: Disable asynchronous scraping (slower but may be more stable)

- `--skip-scraping`: Skip scraping and only match existing events
- `--skip-matching`: Skip event matching
- `--name-similarity`, `-n`: Name similarity threshold for matching (default: 0.85)
- `--team-similarity`, `-t`: Team similarity threshold for matching (default: 0.8)
- `--time-window`, `-w`: Time window in minutes for matching (default: 120)
- `--debug`: Show additional debug information

Examples:

```bash
# Run the full pipeline: scrape all bookmakers and match events
python main.py

# Scrape specific bookmakers with detailed odds and match events
python main.py -b bet777 unibet -d

# Only match existing events (no scraping)
python main.py --skip-scraping

# Only scrape events (no matching)
python main.py --skip-matching

# Customize the matching parameters
python main.py -n 0.8 -t 0.75 -w 180

# Use custom directories
python main.py -o my_data -m my_matched_events
```

#### Separate Scripts

You can also run individual scripts for specific tasks:

```bash

# Run only the matching step
python scripts/match_events.py [OPTIONS]
```

Options for scripts/main.py:
- `--bookmakers`, `-b`: Specify bookmakers to scrape (default: all available)
- `--sport`, `-s`: Sport to scrape (default: football)
- `--detailed-odds`, `-d`: Get detailed odds in addition to basic event data
- `--sequential`: Run scrapers sequentially (not in parallel)
- `--output-dir`, `-o`: Output directory (default: output)
- `--no-async`: Disable asynchronous scraping



Options for scripts/match_events.py:
- `--input-dir`, `-i`: Input directory with scraper output (default: output)
- `--output-dir`, `-o`: Output directory for matched events (default: matched)
- `--name-similarity`, `-n`: Name similarity threshold (default: 0.85)
- `--team-similarity`, `-t`: Team similarity threshold (default: 0.8)
- `--time-window`, `-w`: Time window in minutes (default: 120)

The script will generate three output files:
1. A JSON file with all matched events and their details
2. A CSV comparison table with normalized event data and odds from each bookmaker
3. An Excel file with formatted comparison data for easier analysis
```

### Programmatic Usage

You can also use the scraper programmatically in your own Python code:

```python
from scrapers import ScraperFactory

# For synchronous usage:
# Get a specific scraper
scraper = ScraperFactory.get_scraper("bet777", "football")

# Run the scraper to get basic event data
results = scraper.run(False)

# Run the scraper to get detailed odds data
results_with_odds = scraper.run(True)

# For asynchronous usage (faster):
import asyncio

async def scrape_async():
    # Get an async scraper (all bookmakers supported for async)
    async_scraper = ScraperFactory.get_async_scraper("pinnacle", "football")

    # Run the scraper asynchronously
    results = await async_scraper.run(True)  # True for detailed odds
    return results

# Run the async function
results = asyncio.run(scrape_async())

# You can also get all available async scrapers
all_async_bookmakers = ScraperFactory.get_available_async_bookmakers()

# And run multiple async scrapers in parallel
async def run_all_async_scrapers():
    results = {}
    for bookie in all_async_bookmakers:
        scraper = ScraperFactory.get_async_scraper(bookie, "football")
        results[bookie] = await scraper.run(True)
    return results

all_results = asyncio.run(run_all_async_scrapers())

# Using the event matcher to match events across bookmakers
from models import EventMatcher

# Create an event matcher with custom settings
matcher = EventMatcher(
    name_similarity_threshold=0.8,
    team_similarity_threshold=0.75,
    time_window_minutes=60
)

# Match events across bookmakers
matched_events = matcher.match_events(events_by_bookmaker)

# Get a list of all matched events
events_list = matcher.export_to_dict_list()

# Get a comparison table format
comparison_table = matcher.export_to_comparison_table()

# Export to pandas DataFrame for analysis
import pandas as pd
df = pd.DataFrame(comparison_table)
```

## Project Structure

```
sports_odds_scraper/
├── main.py                # Main entry point for scraping and matching
├── requirements.txt       # Dependencies
├── README.md              # Documentation
├── config/                # Configuration module
│   ├── __init__.py
│   └── config.py          # Configuration settings
├── scripts/               # Utility scripts
│   ├── __init__.py
│   ├── match_events.py    # Script for matching events across bookmakers
│   ├── benchmark_scrapers.py  # Script for benchmarking scrapers
│   └── update_imports.py  # Utility script to update imports
├── scrapers/              # Scraper modules
│   ├── __init__.py        # Package exports
│   ├── core/              # Core scraper components
│   │   ├── __init__.py
│   │   ├── base_scraper.py       # Base scraper class
│   │   ├── async_base_scraper.py # Base async scraper class
│   │   └── scraper_factory.py    # Factory for creating scrapers
│   └── bookies/           # Bookmaker-specific scrapers
│       ├── __init__.py
│       ├── bet777/
│       │   ├── __init__.py
│       │   ├── bet777_scraper.py
│       │   └── async_bet777_scraper.py
│       ├── betcenter/
│       │   ├── __init__.py
│       │   ├── betcenter_scraper.py
│       │   └── async_betcenter_scraper.py
│       ├── betfirst/
│       │   ├── __init__.py
│       │   ├── betfirst_scraper.py
│       │   └── async_betfirst_scraper.py
│       ├── napoleon/
│       │   ├── __init__.py
│       │   ├── napoleon_scraper.py
│       │   └── async_napoleon_scraper.py
│       ├── pinnacle/
│       │   ├── __init__.py
│       │   ├── pinnacle_scraper.py
│       │   └── async_pinnacle_scraper.py
│       └── unibet/
│           ├── __init__.py
│           ├── unibet_scraper.py
│           └── async_unibet_scraper.py
├── models/                # Data models
│   ├── __init__.py
│   ├── event.py           # Event model
│   └── event_matcher.py   # Event matching functionality
├── analyzers/             # Analysis modules (currently disabled)
│   └── __init__.py
├── tests/                 # Test scripts
│   ├── __init__.py
│   ├── test_async_scrapers.py  # Testing and benchmarking async scrapers
│   └── test_event_matcher.py   # Testing event matching functionality
└── utils/                 # Utility functions
    ├── __init__.py
    └── file_handler.py    # File handling utilities
```

## Extending the Framework

### Adding a New Bookmaker

1. Create a new bookmaker directory structure:

```bash
mkdir -p scrapers/bookies/new_bookmaker
touch scrapers/bookies/new_bookmaker/__init__.py
```

2. Create a new synchronous scraper class:

```python
# File: scrapers/bookies/new_bookmaker/new_bookmaker_scraper.py
from scrapers.core.base_scraper import BaseScraper

class NewBookmakerScraper(BaseScraper):
    def __init__(self, bookie_name: str = "new_bookmaker", sport: str = "football", output_dir: str = "output"):
        super().__init__(bookie_name, sport, output_dir)

    def scrape_events(self):
        # Implement scraping logic for basic event data
        pass

    def scrape_odds(self, events=None):
        # Implement scraping logic for detailed odds
        pass
```

3. Create a corresponding asynchronous scraper class for better performance:

```python
# File: scrapers/bookies/new_bookmaker/async_new_bookmaker_scraper.py
import aiohttp
from scrapers.core.async_base_scraper import AsyncBaseScraper

class AsyncNewBookmakerScraper(AsyncBaseScraper):
    def __init__(self, bookie_name: str = "new_bookmaker", sport: str = "football", output_dir: str = "output"):
        super().__init__(bookie_name, sport, output_dir)

    async def scrape_events(self):
        # Implement asynchronous scraping logic for basic event data
        async with aiohttp.ClientSession() as session:
            # Use aiohttp instead of requests for async HTTP requests
            pass

    async def scrape_odds(self, events=None):
        # Implement asynchronous scraping logic for detailed odds
        pass
```

4. Update the bookmaker's `__init__.py` file:

```python
# File: scrapers/bookies/new_bookmaker/__init__.py
from scrapers.bookies.new_bookmaker.new_bookmaker_scraper import NewBookmakerScraper
from scrapers.bookies.new_bookmaker.async_new_bookmaker_scraper import AsyncNewBookmakerScraper

__all__ = [
    'NewBookmakerScraper',
    'AsyncNewBookmakerScraper'
]
```

5. Register both scrapers in `scraper_factory.py`:

```python
# In scrapers/core/scraper_factory.py, add to imports:
from scrapers.bookies.new_bookmaker import NewBookmakerScraper, AsyncNewBookmakerScraper

# Add to the _scrapers dictionary for synchronous scraping
ScraperFactory._scrapers["new_bookmaker"] = NewBookmakerScraper

# Add to the _async_scrapers dictionary for asynchronous scraping
ScraperFactory._async_scrapers["new_bookmaker"] = AsyncNewBookmakerScraper
```

### Adding a New Sport

The framework is designed to be extended to support other sports. To add a new sport:

1. Update the `AVAILABLE_SPORTS` list in `config/config.py`
2. Modify existing scrapers to handle the new sport type
3. Create sport-specific scraper classes if needed

## License

This project is licensed under the MIT License - see the LICENSE file for details.
