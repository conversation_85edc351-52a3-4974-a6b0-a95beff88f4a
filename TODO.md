TODO:
- retries for scraping
- tweak matching and normalization, ditch the acca bets (X win AND btts), detect if first half/second half/full time
- fix shitty arbitrage finder
- focus on certain (type of) odds more than others
- exclude pinnacle from betting, only use it for comparing
- other sports scraping
- direct link to event (and betslip if possible)

FOR LATER:
- betting bot
- constitute own database of leagues, competitions, teams,... with all the corresponding IDs from each bookie, for better matching and indexing
- historical data scraping for match/players stats analysis
- ML score, results, player performance prediction models
- betting "recommandation"
- various betting advices (bankroll managment, strategies, arbitrage +EV, etc...)
- sentiment analysis