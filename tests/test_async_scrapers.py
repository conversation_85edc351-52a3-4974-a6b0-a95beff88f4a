#!/usr/bin/env python3
"""
Script to test the asynchronous scrapers.
"""

import asyncio
import argparse
import time
from typing import Dict, Any

from scrapers import ScraperFactory


async def test_async_scraper(bookie_name: str, detailed_odds: bool = False) -> Dict[str, Any]:
    """
    Test an asynchronous scraper
    
    Args:
        bookie_name: Name of the bookmaker to test
        detailed_odds: Whether to scrape detailed odds
    
    Returns:
        Results from the scraper
    """
    print(f"\nTesting async scraper for {bookie_name}...")
    start_time = time.time()
    
    scraper = ScraperFactory.get_async_scraper(bookie_name)
    result = await scraper.run(detailed_odds)
    
    duration = time.time() - start_time
    print(f"✓ Completed {bookie_name} async scraping in {duration:.2f} seconds")
    
    return result


async def test_sync_scraper(bookie_name: str, detailed_odds: bool = False) -> Dict[str, Any]:
    """
    Test a synchronous scraper in an async wrapper
    
    Args:
        bookie_name: Name of the bookmaker to test
        detailed_odds: Whether to scrape detailed odds
    
    Returns:
        Results from the scraper
    """
    print(f"\nTesting sync scraper for {bookie_name}...")
    start_time = time.time()
    
    # Run synchronous code in a thread pool
    scraper = ScraperFactory.get_scraper(bookie_name)
    
    # Use run_in_executor to run the blocking code in a thread pool
    loop = asyncio.get_event_loop()
    result = await loop.run_in_executor(None, lambda: scraper.run(detailed_odds))
    
    duration = time.time() - start_time
    print(f"✓ Completed {bookie_name} sync scraping in {duration:.2f} seconds")
    
    return result


async def benchmark_all_scrapers(detailed_odds: bool = False):
    """
    Benchmark all scrapers (sync vs async)
    
    Args:
        detailed_odds: Whether to scrape detailed odds
    """
    bookmakers = ScraperFactory.get_available_bookmakers()
    async_bookmakers = ScraperFactory.get_available_async_bookmakers()
    
    # Test async scrapers
    print("\n===== Testing Asynchronous Scrapers =====")
    start_time = time.time()
    async_tasks = [test_async_scraper(bookie, detailed_odds) for bookie in async_bookmakers]
    async_results = await asyncio.gather(*async_tasks)
    async_duration = time.time() - start_time
    
    # Test sync scrapers
    print("\n===== Testing Synchronous Scrapers =====")
    start_time = time.time()
    sync_tasks = [test_sync_scraper(bookie, detailed_odds) for bookie in bookmakers]
    sync_results = await asyncio.gather(*sync_tasks)
    sync_duration = time.time() - start_time
    
    # Print results
    print("\n===== Benchmark Results =====")
    print(f"Asynchronous scrapers total time: {async_duration:.2f} seconds")
    print(f"Synchronous scrapers total time: {sync_duration:.2f} seconds")
    print(f"Speedup factor: {sync_duration / async_duration:.2f}x")


async def test_single_bookmaker(bookie_name: str, detailed_odds: bool = False):
    """
    Test a single bookmaker (sync vs async)
    
    Args:
        bookie_name: Name of the bookmaker to test
        detailed_odds: Whether to scrape detailed odds
    """
    print(f"\n===== Benchmarking {bookie_name} =====")
    
    # Test async scraper
    async_result = await test_async_scraper(bookie_name, detailed_odds)
    async_event_count = len(async_result.get('events_data', []))
    
    # Test sync scraper
    sync_result = await test_sync_scraper(bookie_name, detailed_odds)
    sync_event_count = len(sync_result.get('events_data', []))
    
    # Validate results
    print(f"\nAsync scraper found {async_event_count} events")
    print(f"Sync scraper found {sync_event_count} events")
    
    if async_event_count != sync_event_count:
        print("WARNING: Event counts differ between async and sync implementations")
    else:
        print("✓ Event counts match between implementations")


async def main():
    """Main function"""
    parser = argparse.ArgumentParser(description='Test asynchronous scrapers')
    parser.add_argument('--bookmaker', '-b', help='Specific bookmaker to test')
    parser.add_argument('--detailed-odds', '-d', action='store_true', help='Get detailed odds')
    args = parser.parse_args()
    
    # Get available bookmakers
    bookmakers = ScraperFactory.get_available_bookmakers()
    async_bookmakers = ScraperFactory.get_available_async_bookmakers()
    
    print("Available bookmakers:")
    print(f"- All: {', '.join(bookmakers)}")
    print(f"- Async capable: {', '.join(async_bookmakers)}")
    
    if args.bookmaker:
        if args.bookmaker not in bookmakers:
            print(f"Error: Unknown bookmaker '{args.bookmaker}'")
            return
            
        if args.bookmaker not in async_bookmakers:
            print(f"Error: Bookmaker '{args.bookmaker}' does not have an async implementation")
            return
            
        await test_single_bookmaker(args.bookmaker, args.detailed_odds)
    else:
        await benchmark_all_scrapers(args.detailed_odds)


if __name__ == "__main__":
    asyncio.run(main())