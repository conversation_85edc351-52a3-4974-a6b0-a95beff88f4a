#!/usr/bin/env python3
"""
Script to test the event matching functionality with sample data.
"""

import json
from models import EventMatcher


def create_sample_data():
    """Create sample event data for testing"""
    bet777_events = [
        {
            "ID": "1",
            "Name": "FC Barcelona - Real Madrid",
            "Home": "FC Barcelona",
            "Away": "Real Madrid",
            "Start": "2025-04-05T20:00:00",
            "Country": "Spain",
            "Competition": "La Liga",
            "1": 2.5,
            "X": 3.4,
            "2": 2.8
        },
        {
            "ID": "2",
            "Name": "Liverpool - Manchester United",
            "Home": "Liverpool",
            "Away": "Manchester United",
            "Start": "2025-04-05T15:00:00",
            "Country": "England",
            "Competition": "Premier League",
            "1": 1.8,
            "X": 3.6,
            "2": 4.2
        }
    ]
    
    pinnacle_events = [
        {
            "ID": "101",
            "Name": "Barcelona - R Madrid",
            "Home": "Barcelona",
            "Away": "R Madrid",
            "Start": "2025-04-05T20:05:00",
            "Country": "Spain",
            "Competition": "Spanish League",
            "1": 2.4,
            "X": 3.5,
            "2": 2.9
        },
        {
            "ID": "102",
            "Name": "Bayern Munich - Borussia Dortmund",
            "Home": "Bayern Munich",
            "Away": "Borussia Dortmund",
            "Start": "2025-04-05T18:30:00",
            "Country": "Germany",
            "Competition": "Bundesliga",
            "1": 1.6,
            "X": 4.0,
            "2": 5.5
        }
    ]
    
    betcenter_events = [
        {
            "ID": "201",
            "Name": "Liverpool FC - Manchester Utd",
            "Home": "Liverpool FC",
            "Away": "Manchester Utd",
            "Start": "2025-04-05T14:55:00",
            "Country": "England",
            "Competition": "EPL",
            "1": 1.9,
            "X": 3.5,
            "2": 4.0
        },
        {
            "ID": "202",
            "Name": "Bayer Munich - BVB Dortmund",
            "Home": "Bayer Munich",
            "Away": "BVB Dortmund",
            "Start": "2025-04-05T18:35:00",
            "Country": "Germany",
            "Competition": "Bundesliga",
            "1": 1.65,
            "X": 3.9,
            "2": 5.2
        }
    ]
    
    return {
        "bet777": bet777_events,
        "pinnacle": pinnacle_events,
        "betcenter": betcenter_events
    }


def test_event_matcher():
    """Test the event matcher with sample data"""
    print("Testing EventMatcher with sample data")
    print("===================================\n")
    
    # Create sample data
    events_by_bookmaker = create_sample_data()
    
    # Display input data
    print("Input events by bookmaker:")
    for bookmaker, events in events_by_bookmaker.items():
        print(f"  {bookmaker}: {len(events)} events")
        for event in events:
            print(f"    - {event['Name']} ({event['Start']})")
    
    print("\nTesting with default parameters:")
    # Create matcher with default parameters
    matcher = EventMatcher()
    matched_events = matcher.match_events(events_by_bookmaker)
    
    # Display results
    print(f"Found {len(matched_events)} matched events:")
    for i, event in enumerate(matched_events):
        print(f"  {i+1}. {event.name} ({event.start_time})")
        print(f"     Bookmakers: {', '.join(event.get_bookmakers())}")
        print(f"     Home: {event.home}, Away: {event.away}")
        print("     Odds:")
        for bookie, e in event.bookmaker_events.items():
            print(f"       {bookie}: 1={e.odds_1}, X={e.odds_X}, 2={e.odds_2}")
        print()
    
    # Test with lower thresholds
    print("\nTesting with lower similarity thresholds:")
    matcher = EventMatcher(
        name_similarity_threshold=0.7,
        team_similarity_threshold=0.6
    )
    matched_events = matcher.match_events(events_by_bookmaker)
    
    # Display results
    print(f"Found {len(matched_events)} matched events:")
    for i, event in enumerate(matched_events):
        print(f"  {i+1}. {event.name} ({event.start_time})")
        print(f"     Bookmakers: {', '.join(event.get_bookmakers())}")
    
    # Test comparison table export
    comparison_table = matcher.export_to_comparison_table()
    print(f"\nGenerated comparison table with {len(comparison_table)} rows")
    
    # Save comparison table as JSON for inspection
    with open('test_comparison_table.json', 'w') as f:
        json.dump(comparison_table, f, indent=2)
    
    print("Saved comparison table to test_comparison_table.json")
    print("\nTest completed!")


if __name__ == "__main__":
    test_event_matcher()