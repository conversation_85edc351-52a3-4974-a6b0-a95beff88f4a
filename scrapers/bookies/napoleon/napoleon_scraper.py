import datetime
import requests
import json
from typing import List, Dict, Any, Optional
from tqdm import tqdm
from pathlib import Path

from scrapers.core.base_scraper import BaseScraper


class <PERSON><PERSON><PERSON><PERSON>er(BaseScraper):
    """Scraper for <PERSON>"""

    def __init__(self, bookie_name: str = "napoleon", sport: str = "football", output_dir: str = "output"):
        super().__init__(bookie_name, sport, output_dir)
        self.structure = self._load_structure()

    def _load_structure(self) -> Dict[str, Any]:
        """Load Napoleon structure data"""
        # Try to load existing structure
        structure_path = Path(f'{self.output_dir}/{self.bookie}/napoleon_structure.json')

        if structure_path.exists():
            try:
                with open(structure_path, 'r') as file:
                    return json.load(file).get('data', {})
            except (json.JSONDecodeError, FileNotFoundError) as e:
                print(f"Error loading structure file: {e}")
                return self._create_empty_structure()
        else:
            return self._create_empty_structure()

    def _create_empty_structure(self) -> Dict[str, Any]:
        """Create empty structure dictionary"""
        # Create directory if it doesn't exist
        structure_dir = Path(f'{self.output_dir}/{self.bookie}')
        structure_dir.mkdir(parents=True, exist_ok=True)

        # Create an empty structure
        empty_structure = {
            "categories": [],
            "tournaments": []
        }

        # Save the empty structure
        structure_path = structure_dir / "napoleon_structure.json"
        with open(structure_path, 'w') as file:
            json.dump({"data": empty_structure}, file, indent=4, ensure_ascii=False)

        return empty_structure

    @staticmethod
    def find_name_by_id(data: List[Dict[str, Any]], target_id: str) -> Optional[str]:
        """Helper method to find name by ID"""
        if not data:
            return None

        for item in data:
            if item.get('id') == target_id and 'localNames' in item and 'en-BE' in item['localNames']:
                return item['localNames']['en-BE']
        return None

    def scrape_events(self) -> List[Dict[str, Any]]:
        """
        Scrape basic event data without odds

        Returns:
            List of events with basic metadata
        """
        today = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        tomorrow = (datetime.datetime.now() + datetime.timedelta(days=1)).strftime("%Y-%m-%d %H:%M:%S")

        url = "https://production-superbet-offer-ng-be.freetls.fastly.net/v2/en-BE/events/by-date?"
        params = {
            "offerState": "prematch",
            "startDate": today,
            "endDate": tomorrow,
            "sportId": 5  # Assuming 5 is for football
        }

        try:
            response = requests.get(url, params=params)
            matches = response.json()
        except Exception as e:
            print(f"Error fetching events from Napoleon: {e}")
            return []

        if 'data' not in matches:
            print(f"Unexpected response format from Napoleon: {matches}")
            return []

        all_football_events = []
        for data in matches['data']:
            try:
                # No odds in the initial scrape

                # Handle match name with potential errors
                match_name = data.get('matchName', '')
                if '·' in match_name:
                    home = match_name.split('·')[0]
                    away = match_name.split('·')[1]
                    display_name = match_name.replace('·', ' - ')
                else:
                    # Fallback if separator not found
                    display_name = match_name
                    home = match_name
                    away = "Unknown"

                # Create event data
                all_football_events.append({
                    "ID": data.get('eventId'),
                    "Name": display_name,
                    "HomeTeamID": data.get('homeTeamId'),
                    "Home": home,
                    "AwayTeamID": data.get('awayTeamId'),
                    "Away": away,
                    "CountryID": data.get('categoryId'),
                    "CompetitionID": data.get('tournamentId'),
                    "Start": data.get('matchDate')
                })
            except Exception as e:
                print(f"Error processing Napoleon event: {e}")
                continue

        print(f"Found {len(all_football_events)} matches for {self.bookie}")
        return all_football_events

    def scrape_odds(self, events: Optional[List[Dict[str, Any]]] = None) -> List[Dict[str, Any]]:
        """
        Scrape detailed odds for events

        Args:
            events: List of events to scrape odds for (if None, uses self.events_data)

        Returns:
            List of events with detailed odds
        """
        if events is None:
            events = self.events_data

        if not events:
            return []

        all_odds = []
        for event in tqdm(events, desc=f"Scraping detailed odds for {self.bookie}"):
            try:
                response = requests.get(
                    f"https://production-superbet-offer-ng-be.freetls.fastly.net/v2/en-BE/events/{event['ID']}"
                )

                if response.status_code != 200:
                    print(f"Failed to get detailed odds for event {event['ID']}: {response.status_code}")
                    all_odds.append(event.copy())
                    continue

                data = response.json()
                if not data.get('data'):
                    print(f"No data found for event {event['ID']}")
                    all_odds.append(event.copy())
                    continue

                match = data['data'][0]

                # Create a dictionary to hold all odds
                odds_dict = event.copy()

                # Add country and competition names if available
                country = self.find_name_by_id(
                    self.structure.get('categories', []),
                    str(match.get('categoryId', ''))
                )
                if country:
                    odds_dict["Country"] = country

                competition = self.find_name_by_id(
                    self.structure.get('tournaments', []),
                    str(match.get('tournamentId', ''))
                )
                if competition:
                    odds_dict["Competition"] = competition

                # Extract all available odds
                if match.get('odds'):
                    for odd in [o for o in match['odds'] if all(x not in o.get('marketName', '') for x in [";", "&", "or"])]:
                        column_name = f"{odd.get('marketName', '')}_{odd.get('name', '')}"
                        odds_dict[column_name] = odd.get('price')

                all_odds.append(odds_dict)
            except Exception as e:
                print(f"Error processing odds for event {event.get('ID')}: {e}")
                all_odds.append(event.copy())

        return all_odds