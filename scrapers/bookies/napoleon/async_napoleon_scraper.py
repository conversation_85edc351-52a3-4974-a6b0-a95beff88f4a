import datetime
import json
import aiohttp
from typing import List, Dict, Any, Optional
from tqdm.asyncio import tqdm_asyncio
from pathlib import Path

from scrapers.core.async_base_scraper import AsyncBaseScraper


class AsyncNapoleonScraper(AsyncBaseScraper):
    """Asynchronous scraper for <PERSON>"""

    def __init__(self, bookie_name: str = "napoleon", sport: str = "football", output_dir: str = "output"):
        super().__init__(bookie_name, sport, output_dir)
        self.structure = self._load_structure()

    def _load_structure(self) -> Dict[str, Any]:
        """Load Napoleon structure data"""
        # Try to load existing structure
        structure_path = Path(f'{self.output_dir}/{self.bookie}/napoleon_structure.json')

        if structure_path.exists():
            try:
                with open(structure_path, 'r') as file:
                    return json.load(file).get('data', {})
            except (json.JSONDecodeError, FileNotFoundError) as e:
                print(f"Error loading structure file: {e}")
                return self._create_empty_structure()
        else:
            return self._create_empty_structure()

    def _create_empty_structure(self) -> Dict[str, Any]:
        """Create empty structure dictionary"""
        # Create directory if it doesn't exist
        structure_dir = Path(f'{self.output_dir}/{self.bookie}')
        structure_dir.mkdir(parents=True, exist_ok=True)

        # Create an empty structure
        empty_structure = {
            "categories": [],
            "tournaments": []
        }

        # Save the empty structure
        structure_path = structure_dir / "napoleon_structure.json"
        with open(structure_path, 'w') as file:
            json.dump({"data": empty_structure}, file, indent=4, ensure_ascii=False)

        return empty_structure

    @staticmethod
    def find_name_by_id(data: List[Dict[str, Any]], target_id: str) -> Optional[str]:
        """Helper method to find name by ID"""
        if not data:
            return None

        for item in data:
            if item.get('id') == target_id and 'localNames' in item and 'en-BE' in item['localNames']:
                return item['localNames']['en-BE']
        return None

    async def scrape_events(self) -> List[Dict[str, Any]]:
        """
        Scrape basic event data including 1X2 odds asynchronously

        Returns:
            List of events with basic metadata and 1X2 odds
        """
        today = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        tomorrow = (datetime.datetime.now() + datetime.timedelta(days=1)).strftime("%Y-%m-%d %H:%M:%S")

        url = "https://production-superbet-offer-ng-be.freetls.fastly.net/v2/en-BE/events/by-date"
        params = {
            "offerState": "prematch",
            "startDate": today,
            "endDate": tomorrow,
            "sportId": 5  # Assuming 5 is for football
        }

        try:
            async with aiohttp.ClientSession() as session:
                async with session.get(url, params=params) as response:
                    matches = await response.json()

                    if 'data' not in matches:
                        print(f"Unexpected response format from Napoleon: {matches}")
                        return []

                    all_football_events = []
                    for data in matches['data']:
                        try:
                            # No odds in the initial scrape

                            # Handle match name with potential errors
                            match_name = data.get('matchName', '')
                            if '·' in match_name:
                                home = match_name.split('·')[0]
                                away = match_name.split('·')[1]
                                display_name = match_name.replace('·', ' - ')
                            else:
                                # Fallback if separator not found
                                display_name = match_name
                                home = match_name
                                away = "Unknown"

                            # Create event data
                            all_football_events.append({
                                "ID": data.get('eventId'),
                                "Name": display_name,
                                "HomeTeamID": data.get('homeTeamId'),
                                "Home": home,
                                "AwayTeamID": data.get('awayTeamId'),
                                "Away": away,
                                "CountryID": data.get('categoryId'),
                                "CompetitionID": data.get('tournamentId'),
                                "Start": data.get('matchDate')
                            })
                        except Exception as e:
                            print(f"Error processing Napoleon event: {e}")
                            continue

                    print(f"Found {len(all_football_events)} matches for {self.bookie}")
                    return all_football_events

        except Exception as e:
            print(f"Error fetching events from Napoleon: {e}")
            return []

    async def _get_detailed_odds_for_event(self, session: aiohttp.ClientSession, event: Dict[str, Any]) -> Dict[str, Any]:
        """
        Get detailed odds for a single event asynchronously

        Args:
            session: aiohttp client session
            event: Event data

        Returns:
            Event with detailed odds added
        """
        try:
            async with session.get(
                f"https://production-superbet-offer-ng-be.freetls.fastly.net/v2/en-BE/events/{event['ID']}"
            ) as response:
                if response.status != 200:
                    print(f"Failed to get detailed odds for event {event['ID']}: {response.status}")
                    return event.copy()

                data = await response.json()
                if not data.get('data'):
                    print(f"No data found for event {event['ID']}")
                    return event.copy()

                match = data['data'][0]

                # Create a dictionary to hold all odds
                odds_dict = event.copy()

                # Add country and competition names if available
                country = self.find_name_by_id(
                    self.structure.get('categories', []),
                    str(match.get('categoryId', ''))
                )
                if country:
                    odds_dict["Country"] = country

                competition = self.find_name_by_id(
                    self.structure.get('tournaments', []),
                    str(match.get('tournamentId', ''))
                )
                if competition:
                    odds_dict["Competition"] = competition

                # Extract all available odds
                if match.get('odds'):
                    for odd in [o for o in match['odds'] if all(x not in o.get('marketName', '') for x in [";", "&", "or"])]:
                        column_name = f"{odd.get('marketName', '')}_{odd.get('name', '')}"
                        odds_dict[column_name] = odd.get('price')

                return odds_dict

        except Exception as e:
            print(f"Error getting detailed odds for event {event['ID']}: {e}")
            return event.copy()

    async def scrape_odds(self, event_ids: Optional[List[str]] = None) -> List[Dict[str, Any]]:
        """
        Scrape detailed odds for events asynchronously

        Args:
            event_ids: List of event IDs to scrape odds for (if None, uses all events in self.events_data)

        Returns:
            List of events with detailed odds
        """
        # If event_ids is provided, find those events in the events_data
        if event_ids is not None:
            # Convert event_ids to strings if they aren't already
            event_ids = [str(event_id) for event_id in event_ids]

            # Find the events with the given IDs
            events = []
            for event in self.events_data:
                if str(event.get('ID')) in event_ids:
                    events.append(event)
        else:
            events = self.events_data

        if not events:
            print("No events to scrape odds for")
            return []

        print(f"Scraping detailed odds for {len(events)} events")

        # Use connection pooling with limited concurrency
        connector = aiohttp.TCPConnector(limit=10)  # Limit to 10 concurrent connections

        async with aiohttp.ClientSession(connector=connector) as session:
            # Get detailed odds for all events in parallel
            tasks = [self._get_detailed_odds_for_event(session, event) for event in events]
            all_odds = await tqdm_asyncio.gather(*tasks, desc=f"Scraping detailed odds for {self.bookie}")

            return all_odds