import requests
from typing import List, Dict, Any, Optional
from tqdm import tqdm

from scrapers.core.base_scraper import BaseScraper


class UnibetScraper(BaseScraper):
    """Scraper for Unibet"""

    def __init__(self, bookie_name: str = "unibet", sport: str = "football", output_dir: str = "output"):
        super().__init__(bookie_name, sport, output_dir)

    def scrape_events(self) -> List[Dict[str, Any]]:
        """
        Scrape basic event data without odds

        Returns:
            List of events with basic metadata
        """
        url = "https://fr.unibetsports.be/sportsbook-feeds/views/filter/football/all/matches"
        matches = requests.get(url).json()

        all_football_events = []
        for group in matches['layout']['sections'][1]['widgets'][0]['matches']['groups']:
            for sub in group['subGroups']:
                for event in sub['events']:
                    # Skip events that have already started
                    if event['event']['state'] == 'STARTED':
                        continue

                    # No odds in the initial scrape

                    # Extract country and competition info
                    countryID, country = None, None
                    competitionID, competition = None, None
                    if len(event['event']['path']) == 2:
                        competitionID = event['event']['path'][1]['id']
                        competition = event['event']['path'][1]['englishName']
                    elif len(event['event']['path']) == 3:
                        competitionID = event['event']['path'][2]['id']
                        competition = event['event']['path'][2]['englishName']
                        countryID = event['event']['path'][1]['id']
                        country = event['event']['path'][1]['englishName']

                    # Create event data
                    all_football_events.append({
                        "ID": event['event']['id'],
                        "Name": event['event']['englishName'],
                        "HomeTeamID": event['event']['participants'][1]['participantId'],
                        "Home": event['event']['homeName'],
                        "AwayTeamID": event['event']['participants'][0]['participantId'],
                        "Away": event['event']['awayName'],
                        "CountryID": countryID,
                        "Country": country,
                        "CompetitionID": competitionID,
                        "Competition": competition,
                        "Start": event['event']['start']
                    })

        print(f"Found {len(all_football_events)} matches for {self.bookie}")
        return all_football_events

    def scrape_odds(self, events: Optional[List[Dict[str, Any]]] = None) -> List[Dict[str, Any]]:
        """
        Scrape detailed odds for events

        Args:
            events: List of events to scrape odds for (if None, uses self.events_data)

        Returns:
            List of events with detailed odds
        """
        if events is None:
            events = self.events_data

        all_odds = []
        for event in tqdm(events, desc=f"Scraping detailed odds for {self.bookie}"):
            response = requests.get(
                f"https://eu1.offering-api.kambicdn.com/offering/v2018/ubbe/betoffer/event/{event['ID']}.json"
            )

            if response.status_code != 200:
                print(f"Failed to get detailed odds for event {event['ID']}: {response.status_code}")
                all_odds.append(event.copy())
                continue

            res = response.json()

            # Create a dictionary to hold all odds
            odds_dict = event.copy()

            # Initialize 1X2 odds
            odds_dict["1"] = None
            odds_dict["X"] = None
            odds_dict["2"] = None

            # Extract all available odds
            if 'betOffers' not in res.keys():
                print('No bet offers found for game ID: ', event['ID'])
                all_odds.append(odds_dict)
                continue

            for offer in res['betOffers']:
                bet_type = offer['betOfferType']['englishName']
                criterion = offer['criterion']['englishLabel']

                # Create a market identifier
                market = f"{bet_type}_{criterion}"

                # Process each outcome
                for outcome in offer['outcomes']:
                    if outcome.get('status') == 'OPEN' and 'odds' in outcome:
                        # Format the column name
                        column_name = f"{market}_{outcome['englishLabel']}".replace(
                            event['Home'], '1').replace(event['Away'], '2')

                        # Check if this is a 1X2 market
                        if bet_type == "Match" and criterion == "Full Time":
                            if outcome['englishLabel'] == "1" or outcome['englishLabel'] == event['Home']:
                                odds_dict["1"] = outcome['odds'] / 1000
                            elif outcome['englishLabel'] == "X" or outcome['englishLabel'] == "Draw":
                                odds_dict["X"] = outcome['odds'] / 1000
                            elif outcome['englishLabel'] == "2" or outcome['englishLabel'] == event['Away']:
                                odds_dict["2"] = outcome['odds'] / 1000

                        # Add line value if it exists
                        if 'line' in outcome:
                            line_value = outcome['line']/1000
                            column_name += f"_{line_value}"

                        # Add participant name if it exists
                        if 'participant' in outcome:
                            column_name += f"_{outcome['participant']}"

                        # Store the decimal odds
                        odds_dict[column_name] = outcome['odds']/1000

            all_odds.append(odds_dict)

        return all_odds