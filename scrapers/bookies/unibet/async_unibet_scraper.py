import aiohttp
from typing import List, Dict, Any, Optional
from tqdm.asyncio import tqdm_asyncio

from scrapers.core.async_base_scraper import AsyncBaseScraper


class AsyncUnibetScraper(AsyncBaseScraper):
    """Asynchronous scraper for Unibet"""

    def __init__(self, bookie_name: str = "unibet", sport: str = "football", output_dir: str = "output"):
        super().__init__(bookie_name, sport, output_dir)

    async def scrape_events(self) -> List[Dict[str, Any]]:
        """
        Scrape basic event data including 1X2 odds asynchronously

        Returns:
            List of events with basic metadata and 1X2 odds
        """
        url = "https://fr.unibetsports.be/sportsbook-feeds/views/filter/football/all/matches"

        async with aiohttp.ClientSession() as session:
            async with session.get(url) as response:
                matches = await response.json()

                all_football_events = []
                for group in matches['layout']['sections'][1]['widgets'][0]['matches']['groups']:
                    for sub in group['subGroups']:
                        for event in sub['events']:
                            # Skip events that have already started
                            if event['event']['state'] == 'STARTED':
                                continue

                            # No odds in the initial scrape

                            # Extract country and competition info
                            countryID, country = None, None
                            competitionID, competition = None, None
                            if len(event['event']['path']) == 2:
                                competitionID = event['event']['path'][1]['id']
                                competition = event['event']['path'][1]['englishName']
                            elif len(event['event']['path']) == 3:
                                competitionID = event['event']['path'][2]['id']
                                competition = event['event']['path'][2]['englishName']
                                countryID = event['event']['path'][1]['id']
                                country = event['event']['path'][1]['englishName']

                            # Create event data
                            all_football_events.append({
                                "ID": event['event']['id'],
                                "Name": event['event']['englishName'],
                                "HomeTeamID": event['event']['participants'][1]['participantId'],
                                "Home": event['event']['homeName'],
                                "AwayTeamID": event['event']['participants'][0]['participantId'],
                                "Away": event['event']['awayName'],
                                "CountryID": countryID,
                                "Country": country,
                                "CompetitionID": competitionID,
                                "Competition": competition,
                                "Start": event['event']['start']
                            })

        print(f"Found {len(all_football_events)} matches for {self.bookie}")
        return all_football_events

    async def _get_detailed_odds_for_event(self, session: aiohttp.ClientSession, event: Dict[str, Any]) -> Dict[str, Any]:
        """
        Get detailed odds for a single event asynchronously

        Args:
            session: aiohttp client session
            event: Event data

        Returns:
            Event with detailed odds added
        """
        try:
            async with session.get(
                f"https://eu1.offering-api.kambicdn.com/offering/v2018/ubbe/betoffer/event/{event['ID']}.json"
            ) as response:
                if response.status != 200:
                    print(f"Failed to get detailed odds for event {event['ID']}: {response.status}")
                    return event.copy()

                res = await response.json()

                # Create a dictionary to hold all odds
                odds_dict = event.copy()

                # Extract all available odds
                if 'betOffers' not in res.keys():
                    print('No bet offers found for game ID: ', event['ID'])
                    return odds_dict

                for offer in res['betOffers']:
                    bet_type = offer['betOfferType']['englishName']
                    criterion = offer['criterion']['englishLabel']

                    # Create a market identifier
                    market = f"{bet_type}_{criterion}"

                    # Process each outcome
                    for outcome in offer['outcomes']:
                        if outcome.get('status') == 'OPEN' and 'odds' in outcome:
                            # Format the column name
                            column_name = f"{market}_{outcome['englishLabel']}".replace(
                                event['Home'], '1').replace(event['Away'], '2')

                            # Add line value if it exists
                            if 'line' in outcome:
                                line_value = outcome['line']/1000
                                column_name += f"_{line_value}"

                            # Add participant name if it exists
                            if 'participant' in outcome:
                                column_name += f"_{outcome['participant']}"

                            # Store the decimal odds
                            odds_dict[column_name] = outcome['odds']/1000

                return odds_dict

        except Exception as e:
            print(f"Error getting detailed odds for event {event['ID']}: {e}")
            return event.copy()

    async def scrape_odds(self, event_ids: Optional[List[str]] = None) -> List[Dict[str, Any]]:
        """
        Scrape detailed odds for events asynchronously

        Args:
            event_ids: List of event IDs to scrape odds for (if None, uses all events in self.events_data)

        Returns:
            List of events with detailed odds
        """
        # If event_ids is provided, find those events in the events_data
        if event_ids is not None:
            # Convert event_ids to strings if they aren't already
            event_ids = [str(event_id) for event_id in event_ids]

            # Find the events with the given IDs
            events = []
            for event in self.events_data:
                if str(event.get('ID')) in event_ids:
                    events.append(event)
        else:
            events = self.events_data

        if not events:
            print("No events to scrape odds for")
            return []

        print(f"Scraping detailed odds for {len(events)} events")

        # Use connection pooling with limited concurrency
        connector = aiohttp.TCPConnector(limit=10)  # Limit to 10 concurrent connections

        async with aiohttp.ClientSession(connector=connector) as session:
            # Get detailed odds for all events in parallel
            tasks = [self._get_detailed_odds_for_event(session, event) for event in events]
            all_odds = await tqdm_asyncio.gather(*tasks, desc=f"Scraping detailed odds for {self.bookie}")

            return all_odds