import datetime
import aiohttp
from typing import List, Dict, Any, Optional
from tqdm.asyncio import tqdm_asyncio

from scrapers.core.async_base_scraper import AsyncBaseScraper


class AsyncBet777Scraper(AsyncBaseScraper):
    """Asynchronous scraper for Bet777"""

    def __init__(self, bookie_name: str = "bet777", sport: str = "football", output_dir: str = "output"):
        super().__init__(bookie_name, sport, output_dir)

    async def scrape_events(self) -> List[Dict[str, Any]]:
        """
        Scrape basic event data including 1X2 odds asynchronously

        Returns:
            List of events with basic metadata and 1X2 odds
        """
        events_url = "https://api.sportify.bet/echo/v1/events"
        today = datetime.datetime.now().strftime("%Y-%m-%d")
        payload = {
            "date": today,
            "sport": self.sport,
            "type": "daily",
            "key": "market_type",
            "lang": "fr",
            "bookmaker": self.bookie,
            "id": "1f0aaeef-7374-4511-966a-07d99d6f70ee"
        }

        async with aiohttp.ClientSession() as session:
            async with session.get(events_url, params=payload) as response:
                events = await response.json()

                all_football_events = []
                for comp in events['tree'][0]['competitions']:
                    for event in comp['events']:
                        # Extract home and away teams
                        home, away = None, None
                        home_id, away_id = None, None
                        for competitor in event['competitors']:
                            if competitor['is_home']:
                                home = competitor['name']
                                home_id = competitor['team_id']
                            else:
                                away = competitor['name']
                                away_id = competitor['team_id']

                        # No odds in the initial scrape

                        # Create event data
                        all_football_events.append({
                            "ID": event['id'],
                            "Name": event['name'].replace('vs', '-'),
                            "HomeTeamID": home_id,
                            "Home": home,
                            "AwayTeamID": away_id,
                            "Away": away,
                            "CountryID": event['region_id'],
                            "Country": event['region_name'],
                            "CompetitionID": event['competition_id'],
                            "Competition": event['competition_name'],
                            "Start": event['starts_at']
                        })

        print(f"Found {len(all_football_events)} matches for {self.bookie}")
        return all_football_events

    async def _get_detailed_odds_for_event(self, session: aiohttp.ClientSession, event: Dict[str, Any]) -> Dict[str, Any]:
        """
        Get detailed odds for a single event asynchronously

        Args:
            session: aiohttp client session
            event: Event data

        Returns:
            Event with detailed odds added
        """
        try:
            async with session.get(
                f"https://api.sportify.bet/echo/v1/markets?event_id={event['ID']}&bookmaker=bet777&lang=en#"
            ) as response:
                match = await response.json()

                # Create a dictionary to hold all odds
                odds_dict = event.copy()

                # Process each market
                for market in match['markets']:
                    for outcome in market['outcomes']:
                        column_name = f"{market['name']}_{outcome['name']}"
                        odds_dict[column_name] = outcome['odds']

                return odds_dict

        except Exception as e:
            print(f"Error getting detailed odds for event {event['ID']}: {e}")
            return event.copy()

    async def scrape_odds(self, event_ids: Optional[List[str]] = None) -> List[Dict[str, Any]]:
        """
        Scrape detailed odds for events asynchronously

        Args:
            event_ids: List of event IDs to scrape odds for (if None, uses all events in self.events_data)

        Returns:
            List of events with detailed odds
        """
        # If event_ids is provided, find those events in the events_data
        if event_ids is not None:
            # Convert event_ids to strings if they aren't already
            event_ids = [str(event_id) for event_id in event_ids]

            # Find the events with the given IDs
            events = []
            for event in self.events_data:
                if str(event.get('ID')) in event_ids:
                    events.append(event)
        else:
            events = self.events_data

        if not events:
            print("No events to scrape odds for")
            return []

        print(f"Scraping detailed odds for {len(events)} events")

        # Use connection pooling with limited concurrency
        connector = aiohttp.TCPConnector(limit=10)  # Limit to 10 concurrent connections

        async with aiohttp.ClientSession(connector=connector) as session:
            # Get detailed odds for all events in parallel
            tasks = [self._get_detailed_odds_for_event(session, event) for event in events]
            all_odds = await tqdm_asyncio.gather(*tasks, desc=f"Scraping detailed odds for {self.bookie}")

            return all_odds