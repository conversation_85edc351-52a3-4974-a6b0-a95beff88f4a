import datetime
import requests
from typing import List, Dict, Any, Optional
from tqdm import tqdm

from scrapers.core.base_scraper import BaseScraper


class Bet777Scraper(BaseScraper):
    """Scraper for Bet777"""

    def __init__(self, bookie_name: str = "bet777", sport: str = "football", output_dir: str = "output"):
        super().__init__(bookie_name, sport, output_dir)

    def scrape_events(self) -> List[Dict[str, Any]]:
        """
        Scrape basic event data without odds

        Returns:
            List of events with basic metadata
        """
        events_url = "https://api.sportify.bet/echo/v1/events"
        today = datetime.datetime.now().strftime("%Y-%m-%d")
        payload = {
            "date": today,
            "sport": self.sport,
            "type": "daily",
            "key": "market_type",
            "lang": "fr",
            "bookmaker": self.bookie,
            "id": "1f0aaeef-7374-4511-966a-07d99d6f70ee"
        }

        events = requests.get(events_url, params=payload).json()

        all_football_events = []
        for comp in events['tree'][0]['competitions']:
            for event in comp['events']:
                # Extract home and away teams
                home, away = None, None
                home_id, away_id = None, None
                for competitor in event['competitors']:
                    if competitor['is_home']:
                        home = competitor['name']
                        home_id = competitor['team_id']
                    else:
                        away = competitor['name']
                        away_id = competitor['team_id']

                # No odds in the initial scrape

                # Create event data
                all_football_events.append({
                    "ID": event['id'],
                    "Name": event['name'].replace('vs', '-'),
                    "HomeTeamID": home_id,
                    "Home": home,
                    "AwayTeamID": away_id,
                    "Away": away,
                    "CountryID": event['region_id'],
                    "Country": event['region_name'],
                    "CompetitionID": event['competition_id'],
                    "Competition": event['competition_name'],
                    "Start": event['starts_at']
                })

        print(f"Found {len(all_football_events)} matches for {self.bookie}")
        return all_football_events

    def scrape_odds(self, events: Optional[List[Dict[str, Any]]] = None) -> List[Dict[str, Any]]:
        """
        Scrape detailed odds for events

        Args:
            events: List of events to scrape odds for (if None, uses self.events_data)

        Returns:
            List of events with detailed odds
        """
        if events is None:
            events = self.events_data

        all_odds = []
        for event in tqdm(events, desc=f"Scraping detailed odds for {self.bookie}"):
            # Get detailed odds for the event
            match = requests.get(
                f"https://api.sportify.bet/echo/v1/markets?event_id={event['ID']}&bookmaker=bet777&lang=en#"
            ).json()

            # Create a dictionary to hold all odds
            odds_dict = event.copy()

            # Process each market
            for market in match['markets']:
                for outcome in market['outcomes']:
                    column_name = f"{market['name']}_{outcome['name']}"
                    odds_dict[column_name] = outcome['odds']

            all_odds.append(odds_dict)

        return all_odds