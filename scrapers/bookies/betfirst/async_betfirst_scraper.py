import aiohttp
from typing import List, Dict, Any, Optional
from tqdm.asyncio import tqdm_asyncio

from scrapers.core.async_base_scraper import AsyncBaseScraper


class AsyncBetfirstScraper(AsyncBaseScraper):
    """Asynchronous scraper for Betfirst"""

    def __init__(self, bookie_name: str = "betfirst", sport: str = "football", output_dir: str = "output"):
        super().__init__(bookie_name, sport, output_dir)
        self.token = None

    async def _get_token(self, session: aiohttp.ClientSession) -> str:
        """Get authentication token asynchronously"""
        bearer_url = "https://sbapi.sbtech.com/betfirst/walletauthorization/users/anonymous"
        user_agent = "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/130.0.0.0 Safari/537.36"

        async with session.post(
            bearer_url,
            headers={
                "User-Agent": user_agent,
                "Referer": "https://betfirst.dhnet.be/"
            },
            json={
                "DomainId": "0a57cb53ba59c46fc4b692527a38a87c78d84028"
            }
        ) as response:
            data = await response.json()
            return f"Bearer {data['jwt']}"

    @staticmethod
    def find_selections_by_id(data: List[Dict[str, Any]], target_id: str) -> Optional[List[Dict[str, Any]]]:
        """Helper method to find selections by event ID"""
        for item in data:
            if item['eventId'] == target_id:
                return item['selections']
        return None

    async def scrape_events(self) -> List[Dict[str, Any]]:
        """
        Scrape basic event data including 1X2 odds asynchronously

        Returns:
            List of events with basic metadata and 1X2 odds
        """
        async with aiohttp.ClientSession() as session:
            # Get token if not already set
            if self.token is None:
                self.token = await self._get_token(session)

            events_url = "https://sbapi.sbtech.com/betfirst/sportscontent/sportsbook/v1/events/getpersonalizedprelivebysportId"
            payload = {
                "numberOfEvents": 1500,
                "regionCode": "BE",
                "ids": ["1"],  # Assuming 1 is for football
                "includeFields": ["Events", "Markets"]
            }

            async with session.post(
                events_url,
                headers={"Authorization": self.token},
                json=payload
            ) as response:
                events = await response.json()

                all_football_events = []
                for event in events['events']:
                    if event['isSuspended']:
                        continue

                    # Extract home and away teams
                    home, away = None, None
                    home_id, away_id = None, None
                    for participant in event['participants']:
                        if participant['venueRole'] == 'Home':
                            home = participant['name']
                            home_id = participant['id']
                        elif participant['venueRole'] == 'Away':
                            away = participant['name']
                            away_id = participant['id']

                    # No odds in the initial scrape

                    all_football_events.append({
                        "ID": event['id'],
                        "Name": event['eventName'].replace('vs', '-'),
                        "HomeTeamID": home_id,
                        "Home": home,
                        "AwayTeamID": away_id,
                        "Away": away,
                        "CountryID": event['regionId'],
                        "Country": event['regionName'],
                        "CompetitionID": event['leagueId'],
                        "Competition": event['leagueName'],
                        "Start": event['startEventDate']
                    })

        print(f"Found {len(all_football_events)} matches for {self.bookie}")
        return all_football_events

    async def _get_detailed_odds_for_event(self, session: aiohttp.ClientSession, event: Dict[str, Any]) -> Dict[str, Any]:
        """
        Get detailed odds for a single event asynchronously

        Args:
            session: aiohttp client session
            event: Event data

        Returns:
            Event with detailed odds added
        """
        try:
            user_agent = "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/130.0.0.0 Safari/537.36"

            event_url = f"https://sbapi.sbtech.com/betfirst/sportsdata/v2/events?query=$filter=id%20eq%20%27{event['ID']}%27&includeMarkets=$filter="

            async with session.get(
                event_url,
                headers={
                    "User-Agent": user_agent,
                    "Content-Type": "application/x-www-form-urlencoded",
                    "Authorization": self.token
                }
            ) as response:
                if response.status != 200:
                    print(f"Failed to get detailed odds for event {event['ID']}: {response.status}")
                    return event.copy()

                data = await response.json()
                match = data.get('data', {})

                if not match or 'markets' not in match:
                    print(f"No markets found for event {event['ID']}")
                    return event.copy()

                odds_dict = event.copy()

                # Extract all available odds
                for market in match['markets']:
                    for sel in market['selections']:
                        column_name = f"{market['name']}_{sel['betslipLine']}"
                        odds_dict[column_name] = sel['displayOdds']['decimal']

                return odds_dict

        except Exception as e:
            print(f"Error getting detailed odds for event {event['ID']}: {e}")
            return event.copy()

    async def scrape_odds(self, event_ids: Optional[List[str]] = None) -> List[Dict[str, Any]]:
        """
        Scrape detailed odds for events asynchronously

        Args:
            event_ids: List of event IDs to scrape odds for (if None, uses all events in self.events_data)

        Returns:
            List of events with detailed odds
        """
        # If event_ids is provided, find those events in the events_data
        if event_ids is not None:
            # Convert event_ids to strings if they aren't already
            event_ids = [str(event_id) for event_id in event_ids]

            # Find the events with the given IDs
            events = []
            for event in self.events_data:
                if str(event.get('ID')) in event_ids:
                    events.append(event)
        else:
            events = self.events_data

        if not events:
            print("No events to scrape odds for")
            return []

        print(f"Scraping detailed odds for {len(events)} events")

        # Use connection pooling with limited concurrency
        connector = aiohttp.TCPConnector(limit=10)  # Limit to 10 concurrent connections

        async with aiohttp.ClientSession(connector=connector) as session:
            # Get token if not already set
            if self.token is None:
                self.token = await self._get_token(session)

            # Get detailed odds for all events in parallel
            tasks = [self._get_detailed_odds_for_event(session, event) for event in events]
            all_odds = await tqdm_asyncio.gather(*tasks, desc=f"Scraping detailed odds for {self.bookie}")

            return all_odds