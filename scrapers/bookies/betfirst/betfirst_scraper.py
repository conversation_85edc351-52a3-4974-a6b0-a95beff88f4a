import requests
from typing import List, Dict, Any, Optional
from tqdm import tqdm

from scrapers.core.base_scraper import BaseScraper


class BetfirstScraper(BaseScraper):
    """Scraper for Betfirst"""

    def __init__(self, bookie_name: str = "betfirst", sport: str = "football", output_dir: str = "output"):
        super().__init__(bookie_name, sport, output_dir)
        self.token = None

    def _get_token(self) -> str:
        """Get authentication token"""
        bearer_url = "https://sbapi.sbtech.com/betfirst/walletauthorization/users/anonymous"
        user_agent = "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/130.0.0.0 Safari/537.36"
        r = requests.post(bearer_url, headers={
            "User-Agent": user_agent,
            "Referer": "https://betfirst.dhnet.be/"
        }, json={
            "DomainId": "0a57cb53ba59c46fc4b692527a38a87c78d84028"
        })
        return f"Bearer {r.json()['jwt']}"

    @staticmethod
    def find_selections_by_id(data: List[Dict[str, Any]], target_id: str) -> Optional[List[Dict[str, Any]]]:
        """Helper method to find selections by event ID"""
        for item in data:
            if item['eventId'] == target_id:
                return item['selections']
        return None

    def scrape_events(self) -> List[Dict[str, Any]]:
        """
        Scrape basic event data without odds

        Returns:
            List of events with basic metadata
        """
        # Get token if not already set
        if self.token is None:
            self.token = self._get_token()

        events_url = "https://sbapi.sbtech.com/betfirst/sportscontent/sportsbook/v1/events/getpersonalizedprelivebysportId"
        payload = {
            "numberOfEvents": 1500,
            "regionCode": "BE",
            "ids": ["1"],  # Assuming 1 is for football
            "includeFields": ["Events", "Markets"]
        }

        events = requests.post(
            events_url,
            headers={"Authorization": self.token},
            json=payload
        ).json()

        all_football_events = []
        for event in events['events']:
            if event['isSuspended']:
                continue

            # Extract home and away teams
            home, away = None, None
            home_id, away_id = None, None
            for participant in event['participants']:
                if participant['venueRole'] == 'Home':
                    home = participant['name']
                    home_id = participant['id']
                elif participant['venueRole'] == 'Away':
                    away = participant['name']
                    away_id = participant['id']

            # No odds in the initial scrape

            all_football_events.append({
                "ID": event['id'],
                "Name": event['eventName'].replace('vs', '-'),
                "HomeTeamID": home_id,
                "Home": home,
                "AwayTeamID": away_id,
                "Away": away,
                "CountryID": event['regionId'],
                "Country": event['regionName'],
                "CompetitionID": event['leagueId'],
                "Competition": event['leagueName'],
                "Start": event['startEventDate']
            })

        print(f"Found {len(all_football_events)} matches for {self.bookie}")
        return all_football_events

    def scrape_odds(self, events: Optional[List[Dict[str, Any]]] = None) -> List[Dict[str, Any]]:
        """
        Scrape detailed odds for events

        Args:
            events: List of events to scrape odds for (if None, uses self.events_data)

        Returns:
            List of events with detailed odds
        """
        if events is None:
            events = self.events_data

        # Get token if not already set
        if self.token is None:
            self.token = self._get_token()

        user_agent = "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/130.0.0.0 Safari/537.36"

        all_odds = []
        for event in tqdm(events, desc=f"Scraping detailed odds for {self.bookie}"):
            event_url = f"https://sbapi.sbtech.com/betfirst/sportsdata/v2/events?query=$filter=id%20eq%20%27{event['ID']}%27&includeMarkets=$filter="
            response = requests.get(
                event_url,
                headers={
                    "User-Agent": user_agent,
                    "Content-Type": "application/x-www-form-urlencoded",
                    "Authorization": self.token
                }
            )

            if response.status_code != 200:
                print(f"Failed to get detailed odds for event {event['ID']}: {response.status_code}")
                all_odds.append(event.copy())
                continue

            match = response.json().get('data', {})
            if not match or 'markets' not in match:
                print(f"No markets found for event {event['ID']}")
                all_odds.append(event.copy())
                continue

            odds_dict = event.copy()

            # Extract all available odds
            for market in match['markets']:
                for sel in market['selections']:
                    column_name = f"{market['name']}_{sel['betslipLine']}"
                    odds_dict[column_name] = sel['displayOdds']['decimal']

            all_odds.append(odds_dict)

        return all_odds