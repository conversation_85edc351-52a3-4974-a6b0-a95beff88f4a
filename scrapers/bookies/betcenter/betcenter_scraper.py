import requests
from typing import List, Dict, Any, Optional
from tqdm import tqdm

from scrapers.core.base_scraper import BaseScraper


class BetcenterScraper(BaseScraper):
    """Scraper for Betcenter"""

    def __init__(self, bookie_name: str = "betcenter", sport: str = "football", output_dir: str = "output"):
        super().__init__(bookie_name, sport, output_dir)

    @staticmethod
    def find_market_by_id(data: List[Dict[str, Any]], target_key: str, target_id: Any, return_key: str) -> Any:
        """Helper method to find market by ID"""
        for item in data:
            if item[target_key] == target_id:
                return item[return_key]
        return None

    def scrape_events(self) -> List[Dict[str, Any]]:
        """
        Scrape basic event data without odds

        Returns:
            List of events with basic metadata
        """
        headers = {
            'Accept': 'application/json, text/plain, */*',
            'Accept-Language': 'en-GB,en;q=0.9,en-US;q=0.8,fr;q=0.7,nl;q=0.6,it;q=0.5',
            'Connection': 'keep-alive',
            'Content-Type': 'application/json',
            'Origin': 'https://www.betcenter.be',
            'Referer': 'https://www.betcenter.be/',
            'Sec-Fetch-Dest': 'empty',
            'Sec-Fetch-Mode': 'cors',
            'Sec-Fetch-Site': 'same-site',
            'User-Agent': 'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/134.0.0.0 Safari/537.36',
            'sec-ch-ua': '"Chromium";v="134", "Not:A-Brand";v="24", "Google Chrome";v="134"',
            'sec-ch-ua-mobile': '?0',
            'sec-ch-ua-platform': '"Linux"',
            'x-brand': '7',
            'x-client-country': '21',
            'x-language': '2',
            'x-location': '21',
            'x-system-id': '0',
        }

        # Define payload based on sport
        sport_id = 1  # Assuming 1 is for football
        json_data = {
            'gameTypes': [1, 4, 5],
            'limit': 10000,
            'jurisdictionId': 30,
            'sportId': sport_id
        }

        api_url = 'https://oddsservice.betcenter.be/odds/getGames/9'
        events = requests.post(api_url, headers=headers, json=json_data).json()

        all_football_events = []
        for event in tqdm(events['games'], desc=f"Scraping events for {self.bookie}"):
            home = event['teams'][0]['name']
            home_id = event['teams'][0]['id']
            away = event['teams'][1]['name']
            away_id = event['teams'][1]['id']

            # No odds in the initial scrape

            all_football_events.append({
                "ID": event['id'],
                "Name": f"{home} - {away}",
                "HomeTeamID": home_id,
                "Home": home,
                "AwayTeamID": away_id,
                "Away": away,
                "CountryID": event['countryInfo']['id'],
                "Country": event['countryInfo']['name'],
                "CompetitionID": event['leagueInfo']['id'],
                "Competition": event['leagueInfo']['name'],
                "Start": event['startTime']
            })

        print(f"Found {len(all_football_events)} matches for {self.bookie}")
        return all_football_events

    def scrape_odds(self, events: Optional[List[Dict[str, Any]]] = None) -> List[Dict[str, Any]]:
        """
        Scrape detailed odds for events

        Args:
            events: List of events to scrape odds for (if None, uses self.events_data)

        Returns:
            List of events with detailed odds
        """
        if events is None:
            events = self.events_data

        # For Betcenter, all the odds are already in the events response
        # We just need to process them differently

        # Define payload based on sport
        sport_id = 1  # Assuming 1 is for football
        headers = {
            'Accept': 'application/json, text/plain, */*',
            'Accept-Language': 'en-GB,en;q=0.9,en-US;q=0.8,fr;q=0.7,nl;q=0.6,it;q=0.5',
            'Connection': 'keep-alive',
            'Content-Type': 'application/json',
            'Origin': 'https://www.betcenter.be',
            'Referer': 'https://www.betcenter.be/',
            'User-Agent': 'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/134.0.0.0 Safari/537.36',
            'x-brand': '7',
            'x-language': '2',
        }

        json_data = {
            'gameTypes': [1, 4, 5],
            'limit': 10000,
            'jurisdictionId': 30,
            'sportId': sport_id
        }

        api_url = 'https://oddsservice.betcenter.be/odds/getGames/9'
        full_events = requests.post(api_url, headers=headers, json=json_data).json()

        all_odds = []
        for event in tqdm(full_events['games'], desc=f"Processing odds for {self.bookie}"):
            # Find corresponding event
            event_id = event['id']
            event_data = next((e for e in events if e['ID'] == str(event_id)), None)

            if event_data is None:
                # Try to find by name
                home = event['teams'][0]['name']
                away = event['teams'][1]['name']
                event_name = f"{home} - {away}"
                event_data = next((e for e in events if e['Name'] == event_name), None)

                if event_data is None:
                    # Skip if event not found
                    continue

            odds_dict = event_data.copy()

            for market in event['markets']:
                if market['text'] != 'Who will win the match?':
                    for tip in market['tips']:
                        column_name = f"{market['text']}_{tip['text']}"
                        odds_dict[column_name] = tip['odds']/100

            all_odds.append(odds_dict)

        return all_odds