import datetime
import requests
import json
from typing import List, Dict, Any, Optional
from tqdm import tqdm

from scrapers.core.base_scraper import BaseScraper


class PinnacleScraper(BaseScraper):
    """Scraper for Pinnacle"""

    def __init__(self, bookie_name: str = "pinnacle", sport: str = "football", output_dir: str = "output"):
        super().__init__(bookie_name, sport, output_dir)
        # Initialize API key
        self.headers = self._get_api_headers()

    def _get_api_headers(self) -> Dict[str, str]:
        """
        Get API headers with authentication key

        Returns:
            Dictionary of headers for API requests
        """
        # Get API key
        api_url = "https://www.pinnacle.com/config/app.json"
        api_response = requests.get(api_url).json()
        api_key = api_response['api']['haywire']['apiKey']

        # Set up headers for API requests
        return {
            'X-API-Key': api_key,
            'Accept': 'application/json',
            'Content-Type': 'application/json',
            'User-Agent': 'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/134.0.0.0 Safari/537.36',
            'Referer': 'https://www.pinnacle.com/',
            'X-Device-UUID': 'c68c06fb-e8d06b95-c7d2b7dd-5c3082cc'
        }

    def _convert_odds(self, odds: float) -> float:
        """
        Convert American odds to decimal format

        Args:
            odds: American odds

        Returns:
            Decimal odds
        """
        if odds < 0:
            return round((100/abs(odds))+1, 3)
        else:
            return round((odds/100)+1, 3)

    def _create_market_name(self, market_type: str, period: int, points: Optional[float] = None,
                            side: Optional[str] = None, is_alternate: bool = False) -> str:
        """
        Create a standardized market name

        Args:
            market_type: Type of market (moneyline, spread, total, etc.)
            period: Game period (0 for full time, 1 for 1st half, etc.)
            points: Points for spread or total
            side: Side of the bet (home/away)
            is_alternate: Whether this is an alternate market (not used currently)

        Returns:
            Standardized market name
        """
        period_name = "Full Time" if period == 0 else f"{period}st Half" if period == 1 else f"{period}nd Half"

        if market_type == "moneyline":
            return f"Match_{period_name}"
        elif market_type == "spread":
            if points is not None:
                return f"Asian Handicap_{period_name}_{points}"
            return f"Asian Handicap_{period_name}"
        elif market_type == "total":
            if points is not None:
                return f"Over/Under_Total Goals_{period_name}_{points}"
            return f"Over/Under_Total Goals_{period_name}"
        elif market_type == "team_total":
            if side and points is not None:
                return f"Over/Under_Total Goals by {side}_{period_name}_{points}"
            return f"Over/Under_Total Goals by Team_{period_name}"
        else:
            return f"{market_type}_{period_name}"

    def scrape_events(self) -> List[Dict[str, Any]]:
        """
        Scrape basic event data without odds

        Returns:
            List of events with basic metadata
        """
        # Get all football leagues
        leagues_url = "https://guest.api.arcadia.pinnacle.com/0.1/sports/29/leagues?all=false&brandId=0"
        leagues_response = requests.get(leagues_url, headers=self.headers)
        league_ids = [r['id'] for r in leagues_response.json()]

        # Filter to next day's events
        now = datetime.datetime.now()
        now_next_day = (now + datetime.timedelta(days=7))

        # Collect all football events
        all_football_events = []
        print("Fetching all football events...")
        for league_id in tqdm(league_ids, desc=f"Scraping leagues for {self.bookie}"):
            matchups_url = f"https://guest.api.arcadia.pinnacle.com/0.1/leagues/{league_id}/matchups?brandId=0"

            try:
                matchups_response = requests.get(matchups_url, headers=self.headers).json()

                for matchup in matchups_response:
                    if not matchup.get('parent'):
                        home = matchup['participants'][0]['name']
                        away = matchup['participants'][1]['name']
                    else:
                        home = matchup['parent']['participants'][0]['name']
                        away = matchup['parent']['participants'][1]['name']

                    all_football_events.append({
                        "ID": matchup['id'],
                        "Name": f"{home} - {away}",
                        "HomeTeamID": None,
                        "Home": home,
                        "AwayTeamID": None,
                        "Away": away,
                        "CountryID": None,
                        "Country": matchup['league']['group'],
                        "CompetitionID": matchup['league']['id'],
                        "Competition": matchup['league']['name'],
                        "Start": matchup['startTime'],
                    })
            except Exception as e:
                print(f"Error fetching matchups for league {league_id}: {e}")
                continue

        print(f"Found {len(all_football_events)} matches for {self.bookie}")

        # Filter events to only include those within the next week
        filtered_events = [g for g in all_football_events if
                          datetime.datetime.now() <
                          datetime.datetime.fromisoformat(g['Start'].replace('Z', '+00:00')).replace(tzinfo=None) <
                          now_next_day]

        print(f"Filtered to {len(filtered_events)} matches in the next week")

        return filtered_events

    def scrape_odds(self, events: Optional[List[Dict[str, Any]]] = None) -> List[Dict[str, Any]]:
        """
        Scrape detailed odds for events

        Args:
            events: List of events to scrape odds for (if None, uses self.events_data)

        Returns:
            List of events with detailed odds
        """
        if events is None:
            events = self.events_data

        print(f"Fetching detailed odds for {len(events)} matches...")

        # Process each game to get odds
        all_odds = []
        for game in tqdm(events, desc=f"Scraping detailed odds for {self.bookie}"):
            game_id = game['ID']
            odds_url = f"https://guest.api.arcadia.pinnacle.com/0.1/matchups/{game_id}/markets/related/straight"

            try:
                response = requests.get(odds_url, headers=self.headers, timeout=10)
                if response.status_code != 200:
                    print(f"Error fetching odds for game {game_id}: HTTP {response.status_code}")
                    continue

                try:
                    odds_list = response.json()
                except json.JSONDecodeError:
                    print(f"Error decoding JSON for game {game_id}")
                    continue

                # Copy basic game info
                odds_dict = game.copy()

                # Process each market type
                for market in odds_list:
                    market_type = market.get('type')
                    period = market.get('period', 0)
                    side = market.get('side')
                    points = None

                    # Extract points if available
                    for price in market.get('prices', []):
                        if 'points' in price:
                            points = price['points']
                            break

                    # Process moneyline (1X2) odds
                    if market_type == "moneyline" and period == 0:
                        for price in market.get('prices', []):
                            designation = price.get('designation')
                            if designation == 'home':
                                odds_dict["1"] = self._convert_odds(price['price'])
                                odds_dict[f"Match_Full Time_1_{game['Home']}"] = self._convert_odds(price['price'])
                            elif designation == 'away':
                                odds_dict["2"] = self._convert_odds(price['price'])
                                odds_dict[f"Match_Full Time_2_{game['Away']}"] = self._convert_odds(price['price'])
                            elif designation == 'draw':
                                odds_dict["X"] = self._convert_odds(price['price'])
                                odds_dict[f"Match_Full Time_X"] = self._convert_odds(price['price'])

                    # Process all other market types
                    market_name = self._create_market_name(market_type, period, points, side, market.get('isAlternate', False))

                    for price in market.get('prices', []):
                        designation = price.get('designation')
                        price_points = price.get('points')

                        # Create the column name based on market type
                        if market_type == "spread":
                            if designation == 'home':
                                column_name = f"{market_name}_{game['Home']}_{price_points}"
                            else:
                                column_name = f"{market_name}_{game['Away']}_{price_points}"
                        elif market_type == "total" or market_type == "team_total":
                            column_name = f"{market_name}_{designation.capitalize()}"
                            if price_points is not None:
                                column_name += f"_{price_points}"
                        else:
                            if designation == 'home':
                                column_name = f"{market_name}_1_{game['Home']}"
                            elif designation == 'away':
                                column_name = f"{market_name}_2_{game['Away']}"
                            elif designation == 'draw':
                                column_name = f"{market_name}_X"
                            else:
                                column_name = f"{market_name}_{designation}"

                        # Add the odds to the game dictionary
                        odds_dict[column_name] = self._convert_odds(price['price'])

                # Add Double Chance markets (calculated from 1X2)
                if "1" in odds_dict and "X" in odds_dict and "2" in odds_dict:
                    # Calculate Double Chance odds (1/X, 1/2, X/2)
                    home_odds = odds_dict["1"]
                    draw_odds = odds_dict["X"]
                    away_odds = odds_dict["2"]

                    # Formula for Double Chance: 1/(1/odds1 + 1/odds2)
                    odds_dict["Double Chance_Double Chance_1X"] = round(1 / ((1/home_odds) + (1/draw_odds)), 3)
                    odds_dict["Double Chance_Double Chance_12"] = round(1 / ((1/home_odds) + (1/away_odds)), 3)
                    odds_dict["Double Chance_Double Chance_X2"] = round(1 / ((1/draw_odds) + (1/away_odds)), 3)

                # Only add events that have at least one market with odds
                has_odds = False

                # Check if the event has 1X2 odds
                if "1" in odds_dict and "X" in odds_dict and "2" in odds_dict:
                    has_odds = True
                else:
                    # Check if the event has any other market odds
                    for key in odds_dict.keys():
                        if key not in ["ID", "Name", "HomeTeamID", "Home", "AwayTeamID", "Away", "CountryID", "Country", "CompetitionID", "Competition", "Start"]:
                            has_odds = True
                            break

                if has_odds:
                    all_odds.append(odds_dict)
                else:
                    print(f"Skipping event {game_id} ({odds_dict.get('Name', '')}) - no odds available")

            except Exception as e:
                print(f"Error fetching odds for game {game_id}: {e}")

        return all_odds