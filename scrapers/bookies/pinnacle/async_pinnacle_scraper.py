import datetime
import aiohttp
from typing import List, Dict, Any, Optional
from tqdm.asyncio import tqdm_asyncio

from scrapers.core.async_base_scraper import AsyncBaseScraper


class AsyncPinnacleScraper(AsyncBaseScraper):
    """Asynchronous scraper for Pinnacle"""

    def __init__(self, bookie_name: str = "pinnacle", sport: str = "football", output_dir: str = "output"):
        super().__init__(bookie_name, sport, output_dir)
        self.headers = None
        self.api_key = None

    async def _get_api_headers(self, session: aiohttp.ClientSession) -> Dict[str, str]:
        """
        Get API headers with authentication key asynchronously

        Args:
            session: aiohttp ClientSession

        Returns:
            Dictionary of headers for API requests
        """
        if self.headers is not None:
            return self.headers

        # Get API key
        api_url = "https://www.pinnacle.com/config/app.json"
        async with session.get(api_url) as response:
            api_response = await response.json()
            self.api_key = api_response['api']['haywire']['apiKey']

        # Set up headers for API requests
        self.headers = {
            'X-API-Key': self.api_key,
            'Accept': 'application/json',
            'Content-Type': 'application/json',
            'User-Agent': 'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/134.0.0.0 Safari/537.36',
            'Referer': 'https://www.pinnacle.com/',
            'X-Device-UUID': 'c68c06fb-e8d06b95-c7d2b7dd-5c3082cc'
        }

        return self.headers

    def _convert_odds(self, odds: float) -> float:
        """
        Convert American odds to decimal format

        Args:
            odds: American odds

        Returns:
            Decimal odds
        """
        if odds < 0:
            return round((100/abs(odds))+1, 3)
        else:
            return round((odds/100)+1, 3)

    def _create_market_name(self, market_type: str, period: int, points: Optional[float] = None,
                          side: Optional[str] = None, is_alternate: bool = False) -> str:
        """
        Create a standardized market name

        Args:
            market_type: Type of market (moneyline, spread, total, etc.)
            period: Game period (0 for full time, 1 for 1st half, etc.)
            points: Points for spread or total
            side: Side of the bet (home/away)
            is_alternate: Whether this is an alternate market (not used currently)

        Returns:
            Standardized market name
        """
        period_name = "Full Time" if period == 0 else f"{period}st Half" if period == 1 else f"{period}nd Half"

        if market_type == "moneyline":
            return f"Match_{period_name}"
        elif market_type == "spread":
            if points is not None:
                return f"Asian Handicap_{period_name}_{points}"
            return f"Asian Handicap_{period_name}"
        elif market_type == "total":
            if points is not None:
                return f"Over/Under_Total Goals_{period_name}_{points}"
            return f"Over/Under_Total Goals_{period_name}"
        elif market_type == "team_total":
            if side and points is not None:
                return f"Over/Under_Total Goals by {side}_{period_name}_{points}"
            return f"Over/Under_Total Goals by Team_{period_name}"
        else:
            return f"{market_type}_{period_name}"

    async def _get_all_leagues(self, session: aiohttp.ClientSession) -> List[int]:
        """
        Get all football leagues asynchronously

        Args:
            session: aiohttp ClientSession

        Returns:
            List of league IDs
        """
        headers = await self._get_api_headers(session)
        leagues_url = "https://guest.api.arcadia.pinnacle.com/0.1/sports/29/leagues?all=false&brandId=0"

        async with session.get(leagues_url, headers=headers) as response:
            leagues_response = await response.json()
            return [r['id'] for r in leagues_response]

    async def _get_events_for_league(self, session: aiohttp.ClientSession, league_id: int) -> List[Dict[str, Any]]:
        """
        Get events for a specific league asynchronously

        Args:
            session: aiohttp ClientSession
            league_id: League ID

        Returns:
            List of events for the league
        """
        headers = await self._get_api_headers(session)
        matchups_url = f"https://guest.api.arcadia.pinnacle.com/0.1/leagues/{league_id}/matchups?brandId=0"
        events = []

        try:
            async with session.get(matchups_url, headers=headers) as response:
                if response.status != 200:
                    print(f"Error fetching matchups for league {league_id}: HTTP {response.status}")
                    return []

                matchups_response = await response.json()

                for matchup in matchups_response:
                    if not matchup.get('parent'):
                        home = matchup['participants'][0]['name']
                        away = matchup['participants'][1]['name']
                    else:
                        home = matchup['parent']['participants'][0]['name']
                        away = matchup['parent']['participants'][1]['name']

                    events.append({
                        "ID": matchup['id'],
                        "Name": f"{home} - {away}",
                        "HomeTeamID": None,
                        "Home": home,
                        "AwayTeamID": None,
                        "Away": away,
                        "CountryID": None,
                        "Country": matchup['league']['group'],
                        "CompetitionID": matchup['league']['id'],
                        "Competition": matchup['league']['name'],
                        "Start": matchup['startTime'],
                    })

                return events

        except Exception as e:
            print(f"Error fetching matchups for league {league_id}: {e}")
            return []

    # Removed _get_basic_odds_for_event method as it duplicates functionality in _get_detailed_odds_for_event

    async def _get_detailed_odds_for_event(self, session: aiohttp.ClientSession, event: Dict[str, Any]) -> Dict[str, Any]:
        """
        Get detailed odds for an event asynchronously

        Args:
            session: aiohttp ClientSession
            event: Event data

        Returns:
            Event with detailed odds added
        """
        headers = await self._get_api_headers(session)
        game_id = event['ID']
        odds_url = f"https://guest.api.arcadia.pinnacle.com/0.1/matchups/{game_id}/markets/related/straight"

        try:
            async with session.get(odds_url, headers=headers, timeout=10) as response:
                if response.status != 200:
                    print(f"Error fetching odds for game {game_id}: HTTP {response.status}")
                    return event

                odds_list = await response.json()

                # Create a copy of the event to add odds to
                odds_dict = event.copy()

                # Process each market type
                for market in odds_list:
                    market_type = market.get('type')
                    period = market.get('period', 0)
                    side = market.get('side')
                    points = None

                    # Extract points if available
                    for price in market.get('prices', []):
                        if 'points' in price:
                            points = price['points']
                            break

                    # Process moneyline (1X2) odds
                    if market_type == "moneyline" and period == 0:
                        for price in market.get('prices', []):
                            designation = price.get('designation')
                            if designation == 'home':
                                odds_dict["1"] = self._convert_odds(price['price'])
                                odds_dict[f"Match_Full Time_1_{event['Home']}"] = self._convert_odds(price['price'])
                            elif designation == 'away':
                                odds_dict["2"] = self._convert_odds(price['price'])
                                odds_dict[f"Match_Full Time_2_{event['Away']}"] = self._convert_odds(price['price'])
                            elif designation == 'draw':
                                odds_dict["X"] = self._convert_odds(price['price'])
                                odds_dict[f"Match_Full Time_X"] = self._convert_odds(price['price'])

                    # Process all other market types
                    market_name = self._create_market_name(market_type, period, points, side, market.get('isAlternate', False))

                    for price in market.get('prices', []):
                        designation = price.get('designation')
                        price_points = price.get('points')

                        # Create the column name based on market type
                        if market_type == "spread":
                            if designation == 'home':
                                column_name = f"{market_name}_{event['Home']}_{price_points}"
                            else:
                                column_name = f"{market_name}_{event['Away']}_{price_points}"
                        elif market_type == "total" or market_type == "team_total":
                            column_name = f"{market_name}_{designation.capitalize()}"
                            if price_points is not None:
                                column_name += f"_{price_points}"
                        else:
                            if designation == 'home':
                                column_name = f"{market_name}_1_{event['Home']}"
                            elif designation == 'away':
                                column_name = f"{market_name}_2_{event['Away']}"
                            elif designation == 'draw':
                                column_name = f"{market_name}_X"
                            else:
                                column_name = f"{market_name}_{designation}"

                        # Add the odds to the game dictionary
                        odds_dict[column_name] = self._convert_odds(price['price'])

                # Add Double Chance markets (calculated from 1X2)
                if "1" in odds_dict and "X" in odds_dict and "2" in odds_dict:
                    # Calculate Double Chance odds (1/X, 1/2, X/2)
                    home_odds = odds_dict["1"]
                    draw_odds = odds_dict["X"]
                    away_odds = odds_dict["2"]

                    # Formula for Double Chance: 1/(1/odds1 + 1/odds2)
                    odds_dict["Double Chance_Double Chance_1X"] = round(1 / ((1/home_odds) + (1/draw_odds)), 3)
                    odds_dict["Double Chance_Double Chance_12"] = round(1 / ((1/home_odds) + (1/away_odds)), 3)
                    odds_dict["Double Chance_Double Chance_X2"] = round(1 / ((1/draw_odds) + (1/away_odds)), 3)

                return odds_dict

        except Exception as e:
            print(f"Error fetching detailed odds for game {game_id}: {e}")
            return event

    async def scrape_events(self) -> List[Dict[str, Any]]:
        """
        Scrape basic event data without odds asynchronously

        Returns:
            List of events with basic metadata
        """
        # Filter to next week's events
        now = datetime.datetime.now()
        now_next_week = (now + datetime.timedelta(days=7))

        async with aiohttp.ClientSession() as session:
            # Get all leagues in parallel
            league_ids = await self._get_all_leagues(session)
            print(f"Found {len(league_ids)} leagues")

            # Get events for all leagues in parallel
            tasks = [self._get_events_for_league(session, league_id) for league_id in league_ids]
            league_events = await tqdm_asyncio.gather(*tasks, desc=f"Scraping leagues for {self.bookie}")

            # Flatten the list of events
            all_football_events = [event for events in league_events for event in events]
            print(f"Found {len(all_football_events)} matches for {self.bookie}")

            # Filter events to only include those within the next week
            filtered_events = [g for g in all_football_events if
                              datetime.datetime.now() <
                              datetime.datetime.fromisoformat(g['Start'].replace('Z', '+00:00')).replace(tzinfo=None) <
                              now_next_week]
            print(f"Filtered to {len(filtered_events)} matches in the next week")

            return filtered_events

    async def scrape_odds(self, event_ids: Optional[List[str]] = None) -> List[Dict[str, Any]]:
        """
        Scrape detailed odds for events asynchronously

        Args:
            event_ids: List of event IDs to scrape odds for (if None, uses all events in self.events_data)

        Returns:
            List of events with detailed odds
        """
        # If event_ids is provided, find those events in the events_data
        if event_ids is not None:
            # Convert event_ids to strings if they aren't already
            event_ids = [str(event_id) for event_id in event_ids]

            # Find the events with the given IDs
            events = []
            for event in self.events_data:
                if str(event.get('ID')) in event_ids:
                    events.append(event)
        else:
            events = self.events_data

        if not events:
            print("No events to scrape odds for")
            return []

        print(f"Scraping detailed odds for {len(events)} events")

        # Use connection pooling with limited concurrency
        connector = aiohttp.TCPConnector(limit=10)  # Limit to 10 concurrent connections

        async with aiohttp.ClientSession(connector=connector) as session:
            # Get detailed odds for all events in parallel
            tasks = [self._get_detailed_odds_for_event(session, event) for event in events]
            events_with_detailed_odds = await tqdm_asyncio.gather(*tasks, desc=f"Scraping detailed odds for {self.bookie}")

            # Filter out events without odds
            filtered_events = []
            for event in events_with_detailed_odds:
                # Check if the event has odds
                has_odds = False

                # Check if the event has 1X2 odds
                if "1" in event and "X" in event and "2" in event:
                    has_odds = True
                else:
                    # Check if the event has any other market odds
                    for key in event.keys():
                        if key not in ["ID", "Name", "HomeTeamID", "Home", "AwayTeamID", "Away", "CountryID", "Country", "CompetitionID", "Competition", "Start"]:
                            has_odds = True
                            break

                if has_odds:
                    filtered_events.append(event)
                else:
                    print(f"Skipping event {event['ID']} ({event.get('Name', '')}) - no odds available")

            print(f"Filtered from {len(events_with_detailed_odds)} to {len(filtered_events)} events with odds")
            return filtered_events