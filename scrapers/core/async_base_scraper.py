import abc
import json
import asyncio
from pathlib import Path
from typing import List, Dict, Any, Optional


class AsyncBaseScraper(abc.ABC):
    """Base class for all asynchronous scrapers"""

    def __init__(self, bookie_name: str, sport: str = "football", output_dir: str = "output"):
        """
        Initialize the scraper

        Args:
            bookie_name: Name of the bookmaker
            sport: Sport to scrape (default: football)
            output_dir: Directory to store output files (default: output)
        """
        self.bookie = bookie_name
        self.sport = sport
        self.output_dir = output_dir
        self.events_data = []
        self.odds_data = []

        # Ensure directories exist
        self._create_directories()

    def _create_directories(self):
        """Create necessary directories for storing data"""
        Path(f"{self.output_dir}/{self.bookie}/{self.sport}").mkdir(parents=True, exist_ok=True)

    @abc.abstractmethod
    async def scrape_events(self) -> List[Dict[str, Any]]:
        """
        Scrape basic event data without odds asynchronously

        Returns:
            List of events with basic metadata
        """
        pass

    @abc.abstractmethod
    async def scrape_odds(self, events: Optional[List[Dict[str, Any]]] = None) -> List[Dict[str, Any]]:
        """
        Scrape detailed odds for events asynchronously

        Args:
            events: List of events to scrape odds for (if None, uses self.events_data)

        Returns:
            List of events with detailed odds
        """
        pass

    def save_events(self, data: Optional[List[Dict[str, Any]]] = None) -> None:
        """
        Save events data to JSON file

        Args:
            data: Data to save (if None, uses self.events_data)
        """
        if data is None:
            data = self.events_data

        output_path = f'{self.output_dir}/{self.bookie}/{self.sport}/all_{self.sport}_events.json'
        with open(output_path, 'w') as f:
            json.dump(data, f, indent=4, ensure_ascii=False)

        print(f"Saved {len(data)} events to {output_path}")

    def save_odds(self, data: Optional[List[Dict[str, Any]]] = None) -> None:
        """
        Save odds data to JSON file

        Args:
            data: Data to save (if None, uses self.odds_data)
        """
        if data is None:
            data = self.odds_data

        output_path = f'{self.output_dir}/{self.bookie}/{self.sport}/all_{self.sport}_odds.json'
        with open(output_path, 'w') as f:
            json.dump(data, f, indent=4, ensure_ascii=False)

        print(f"Saved {len(data)} odds entries to {output_path}")

    async def run(self, scrape_detailed_odds: bool = False) -> Dict[str, Any]:
        """
        Run the scraper asynchronously

        Args:
            scrape_detailed_odds: Whether to scrape detailed odds

        Returns:
            Dictionary with results
        """
        # Scrape events
        self.events_data = await self.scrape_events()
        self.save_events()

        result = {
            "bookie": self.bookie,
            "sport": self.sport,
            "events_count": len(self.events_data),
            "events_data": self.events_data,
        }

        # Scrape detailed odds if requested
        if scrape_detailed_odds:
            self.odds_data = await self.scrape_odds(self.events_data)
            self.save_odds()
            result["odds_count"] = len(self.odds_data)
            result["odds_data"] = self.odds_data

        return result