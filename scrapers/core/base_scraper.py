import abc
import json
from pathlib import Path
from typing import List, Dict, Any, Optional


class BaseScraper(abc.ABC):
    """Base class for all scrapers"""

    def __init__(self, bookie_name: str, sport: str = "football", output_dir: str = "output"):
        """
        Initialize the scraper

        Args:
            bookie_name: Name of the bookmaker
            sport: Sport to scrape (default: football)
            output_dir: Directory to store output files (default: output)
        """
        self.bookie = bookie_name
        self.sport = sport
        self.output_dir = output_dir
        self.events_data = []
        self.odds_data = []

        # Ensure directories exist
        self._create_directories()

    def _create_directories(self):
        """Create necessary directories for storing data"""
        Path(f"{self.output_dir}/{self.bookie}/{self.sport}").mkdir(parents=True, exist_ok=True)

    @abc.abstractmethod
    def scrape_events(self) -> List[Dict[str, Any]]:
        """
        Scrape basic event data without odds

        Returns:
            List of events with basic metadata
        """
        pass

    @abc.abstractmethod
    def scrape_odds(self, events: Optional[List[Dict[str, Any]]] = None) -> List[Dict[str, Any]]:
        """
        Scrape detailed odds for events

        Args:
            events: List of events to scrape odds for (if None, uses self.events_data)

        Returns:
            List of events with detailed odds
        """
        pass

    def save_events(self, data: Optional[List[Dict[str, Any]]] = None) -> None:
        """
        Save events data to JSON file

        Args:
            data: Data to save (if None, uses self.events_data)
        """
        if data is None:
            data = self.events_data

        # Remove 1X2 odds from events
        cleaned_data = []
        for event in data:
            # Create a copy of the event without 1X2 odds
            cleaned_event = {k: v for k, v in event.items() if k not in ["1", "X", "2"]}
            cleaned_data.append(cleaned_event)

        output_path = f'{self.output_dir}/{self.bookie}/{self.sport}/all_{self.sport}_events.json'
        with open(output_path, 'w') as f:
            json.dump(cleaned_data, f, indent=4, ensure_ascii=False)

        print(f"Saved {len(cleaned_data)} events to {output_path}")

    def save_odds(self, data: Optional[List[Dict[str, Any]]] = None) -> None:
        """
        Save odds data to JSON file

        Args:
            data: Data to save (if None, uses self.odds_data)
        """
        if data is None:
            data = self.odds_data

        output_path = f'{self.output_dir}/{self.bookie}/{self.sport}/all_{self.sport}_odds.json'
        with open(output_path, 'w') as f:
            json.dump(data, f, indent=4, ensure_ascii=False)

        print(f"Saved {len(data)} odds entries to {output_path}")

    def run(self, scrape_detailed_odds: bool = False) -> Dict[str, Any]:
        """
        Run the scraper

        Args:
            scrape_detailed_odds: Whether to scrape detailed odds

        Returns:
            Dictionary with results
        """
        # Scrape events
        self.events_data = self.scrape_events()

        # Remove 1X2 odds from events
        cleaned_events = []
        for event in self.events_data:
            # Create a copy of the event without 1X2 odds
            cleaned_event = {k: v for k, v in event.items() if k not in ["1", "X", "2"]}
            cleaned_events.append(cleaned_event)

        # Save the cleaned events
        self.save_events(cleaned_events)

        result = {
            "bookie": self.bookie,
            "sport": self.sport,
            "events_count": len(cleaned_events),
            "events_data": cleaned_events,
        }

        # Scrape detailed odds if requested
        if scrape_detailed_odds:
            self.odds_data = self.scrape_odds(self.events_data)
            self.save_odds()
            result["odds_count"] = len(self.odds_data)
            result["odds_data"] = self.odds_data

        return result