from typing import Dict, Type, List, Optional, Union

# Base scrapers
from scrapers.core.base_scraper import BaseScraper
from scrapers.core.async_base_scraper import AsyncBaseScraper

# Bookmaker-specific scrapers
from scrapers.bookies.bet777 import Bet777Scraper, AsyncBet777Scraper
from scrapers.bookies.betcenter import Betcenter<PERSON><PERSON>raper, AsyncBetcenterScraper
from scrapers.bookies.betfirst import BetfirstScraper, AsyncBetfirstScraper
from scrapers.bookies.napoleon import <PERSON><PERSON><PERSON><PERSON><PERSON>, AsyncNapoleonScraper
from scrapers.bookies.pinnacle import Pinnacle<PERSON><PERSON>raper, AsyncPinnacleScraper
from scrapers.bookies.unibet import UnibetScraper, AsyncUnibetScraper


class ScraperFactory:
    """Factory class for creating scrapers"""
    
    _scrapers: Dict[str, Type[BaseScraper]] = {
        "bet777": Bet777Scraper,
        "betcenter": BetcenterScraper,
        "betfirst": BetfirstScraper,
        "napoleon": <PERSON><PERSON><PERSON>raper,
        "unibet": UnibetScraper,
        "pinnacle": PinnacleScraper
    }
    
    _async_scrapers: Dict[str, Type[AsyncBaseScraper]] = {
        "pinnacle": AsyncPinnacleScraper,
        "bet777": AsyncBet777Scraper,
        "betcenter": AsyncBetcenterScraper,
        "betfirst": AsyncBetfirstScraper,
        "napoleon": AsyncNapoleonScraper,
        "unibet": AsyncUnibetScraper
    }
    
    @classmethod
    def get_scraper(cls, bookie_name: str, sport: str = "football", output_dir: str = "output") -> BaseScraper:
        """
        Get a scraper instance for the specified bookmaker
        
        Args:
            bookie_name: Name of the bookmaker
            sport: Sport to scrape (default: football)
            output_dir: Directory to save output files (default: output)
            
        Returns:
            Scraper instance
            
        Raises:
            ValueError: If bookmaker is not supported
        """
        if bookie_name not in cls._scrapers:
            raise ValueError(f"Unsupported bookmaker: {bookie_name}. Supported bookmakers: {', '.join(cls._scrapers.keys())}")
        
        return cls._scrapers[bookie_name](bookie_name, sport, output_dir)
    
    @classmethod
    def get_all_scrapers(cls, sport: str = "football", output_dir: str = "output") -> List[BaseScraper]:
        """
        Get all available scraper instances
        
        Args:
            sport: Sport to scrape (default: football)
            output_dir: Directory to save output files (default: output)
            
        Returns:
            List of scraper instances
        """
        return [cls.get_scraper(bookie, sport, output_dir) for bookie in cls._scrapers]
    
    @classmethod
    def register_scraper(cls, bookie_name: str, scraper_class: Type[BaseScraper]) -> None:
        """
        Register a new scraper
        
        Args:
            bookie_name: Name of the bookmaker
            scraper_class: Scraper class to register
        """
        cls._scrapers[bookie_name] = scraper_class
    
    @classmethod
    def get_available_bookmakers(cls) -> List[str]:
        """
        Get list of available bookmakers
        
        Returns:
            List of bookmaker names
        """
        return list(cls._scrapers.keys())
        
    @classmethod
    def get_async_scraper(cls, bookie_name: str, sport: str = "football", output_dir: str = "output") -> AsyncBaseScraper:
        """
        Get an async scraper instance for the specified bookmaker
        
        Args:
            bookie_name: Name of the bookmaker
            sport: Sport to scrape (default: football)
            output_dir: Directory to save output files (default: output)
            
        Returns:
            Async scraper instance
            
        Raises:
            ValueError: If bookmaker is not supported for async scraping
        """
        if bookie_name not in cls._async_scrapers:
            raise ValueError(f"Unsupported bookmaker for async scraping: {bookie_name}. "
                            f"Supported async bookmakers: {', '.join(cls._async_scrapers.keys())}")
        
        return cls._async_scrapers[bookie_name](bookie_name, sport, output_dir)
    
    @classmethod
    def get_all_async_scrapers(cls, sport: str = "football", output_dir: str = "output") -> List[AsyncBaseScraper]:
        """
        Get all available async scraper instances
        
        Args:
            sport: Sport to scrape (default: football)
            output_dir: Directory to save output files (default: output)
            
        Returns:
            List of async scraper instances
        """
        return [cls.get_async_scraper(bookie, sport, output_dir) for bookie in cls._async_scrapers]
        
    @classmethod
    def register_async_scraper(cls, bookie_name: str, scraper_class: Type[AsyncBaseScraper]) -> None:
        """
        Register a new async scraper
        
        Args:
            bookie_name: Name of the bookmaker
            scraper_class: Async scraper class to register
        """
        cls._async_scrapers[bookie_name] = scraper_class
        
    @classmethod
    def get_available_async_bookmakers(cls) -> List[str]:
        """
        Get list of available bookmakers for async scraping
        
        Returns:
            List of bookmaker names that support async scraping
        """
        return list(cls._async_scrapers.keys())