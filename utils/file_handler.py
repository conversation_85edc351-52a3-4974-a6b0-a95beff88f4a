import json
import os
from pathlib import Path
from typing import Dict, List, Any, Optional


class FileHandler:
    """Utility class for handling file operations"""
    
    @staticmethod
    def ensure_directory(directory_path: str) -> None:
        """
        Ensure directory exists
        
        Args:
            directory_path: Path to directory
        """
        Path(directory_path).mkdir(parents=True, exist_ok=True)
    
    @staticmethod
    def save_json(data: Any, file_path: str, indent: int = 4) -> None:
        """
        Save data to JSON file
        
        Args:
            data: Data to save
            file_path: Path to save file to
            indent: JSON indentation level
        """
        # Ensure directory exists
        directory = os.path.dirname(file_path)
        if directory:
            FileHandler.ensure_directory(directory)
        
        # Save data
        with open(file_path, 'w', encoding='utf-8') as f:
            json.dump(data, f, indent=indent, ensure_ascii=False)
    
    @staticmethod
    def load_json(file_path: str) -> Any:
        """
        Load data from JSON file
        
        Args:
            file_path: Path to load file from
            
        Returns:
            Loaded data
        """
        with open(file_path, 'r', encoding='utf-8') as f:
            return json.load(f)
    
    @staticmethod
    def merge_events(events_list: List[List[Dict[str, Any]]]) -> List[Dict[str, Any]]:
        """
        Merge events from multiple sources
        
        Args:
            events_list: List of event lists from different sources
            
        Returns:
            Merged list of events
        """
        merged = []
        for events in events_list:
            merged.extend(events)
        return merged