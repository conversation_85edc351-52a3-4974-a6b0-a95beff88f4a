"""
Script to scrape odds for matched events
"""
import os
import time
import json
import asyncio
from typing import List, Dict, Any
from tqdm import tqdm

from models.event_matcher import EventMatcher
from scrapers import ScraperFactory
from utils.file_handler import FileHandler


def _load_events_for_bookmaker(bookmaker: str, sport: str, output_dir: str) -> List[Dict[str, Any]]:
    """
    Load events from the JSON file for a bookmaker

    Args:
        bookmaker: Bookmaker name
        sport: Sport name
        output_dir: Output directory

    Returns:
        List of events
    """
    events_path = os.path.join(output_dir, bookmaker, sport, f"all_{sport}_events.json")
    if not os.path.exists(events_path):
        print(f"No events file found for {bookmaker} at {events_path}")
        return []

    try:
        with open(events_path, 'r') as f:
            events = json.load(f)
        print(f"Loaded {len(events)} events for {bookmaker}")
        return events
    except Exception as e:
        print(f"Error loading events for {bookmaker}: {e}")
        return []


async def _scrape_odds_async(matcher: EventMatcher, bookmakers: List[str], sport: str,
                           output_dir: str) -> List[Dict[str, Any]]:
    """
    Scrape detailed odds for matched events using async scrapers

    Args:
        matcher: EventMatcher with matched events
        bookmakers: List of bookmakers to scrape
        sport: Sport to scrape
        output_dir: Output directory

    Returns:
        List of events with detailed odds
    """
    # Get all bookmaker IDs for each matched event
    event_ids_by_bookmaker = {}
    for event in matcher.matched_events:
        for bookmaker, evt in event.bookmaker_events.items():
            if bookmaker not in event_ids_by_bookmaker:
                event_ids_by_bookmaker[bookmaker] = []
            event_ids_by_bookmaker[bookmaker].append(evt.ID)

    # Only scrape odds for bookmakers that have matched events
    bookmakers_to_scrape = [b for b in bookmakers if b in event_ids_by_bookmaker]

    if not bookmakers_to_scrape:
        print("No matched events found for the specified bookmakers.")
        return []

    print(f"Scraping detailed odds for {len(bookmakers_to_scrape)} bookmakers with matched events...")

    # Scrape detailed odds for each bookmaker
    all_odds = []
    for bookmaker in bookmakers_to_scrape:
        event_ids = event_ids_by_bookmaker.get(bookmaker, [])

        if not event_ids:
            print(f"No matched events found for {bookmaker}.")
            continue

        print(f"Scraping detailed odds for {len(event_ids)} {bookmaker} events...")

        # Create the scraper
        scraper = ScraperFactory.get_async_scraper(bookmaker, sport, output_dir)

        # Load the events from the JSON file
        events = _load_events_for_bookmaker(bookmaker, sport, output_dir)
        scraper.events_data = events

        # Scrape detailed odds for the matched events
        odds = await scraper.scrape_odds(event_ids)
        if odds:
            # Add bookmaker name to each odds entry
            for odd in odds:
                odd['Bookmaker'] = bookmaker
            all_odds.extend(odds)
            print(f"Scraped {len(odds)} detailed odds for {bookmaker}.")
        else:
            print(f"No odds found for {bookmaker}.")

    return all_odds


def _scrape_odds_sync(matcher: EventMatcher, bookmakers: List[str], sport: str,
                     output_dir: str) -> List[Dict[str, Any]]:
    """
    Scrape detailed odds for matched events using synchronous scrapers

    Args:
        matcher: EventMatcher with matched events
        bookmakers: List of bookmakers to scrape
        sport: Sport to scrape
        output_dir: Output directory

    Returns:
        List of events with detailed odds
    """
    # Get all bookmaker IDs for each matched event
    event_ids_by_bookmaker = {}
    for event in matcher.matched_events:
        for bookmaker, evt in event.bookmaker_events.items():
            if bookmaker not in event_ids_by_bookmaker:
                event_ids_by_bookmaker[bookmaker] = []
            event_ids_by_bookmaker[bookmaker].append(evt.ID)

    # Only scrape odds for bookmakers that have matched events
    bookmakers_to_scrape = [b for b in bookmakers if b in event_ids_by_bookmaker]

    if not bookmakers_to_scrape:
        print("No matched events found for the specified bookmakers.")
        return []

    print(f"Scraping detailed odds for {len(bookmakers_to_scrape)} bookmakers with matched events...")

    # Scrape detailed odds for each bookmaker
    all_odds = []
    for bookmaker in bookmakers_to_scrape:
        event_ids = event_ids_by_bookmaker.get(bookmaker, [])

        if not event_ids:
            print(f"No matched events found for {bookmaker}.")
            continue

        print(f"Scraping detailed odds for {len(event_ids)} {bookmaker} events...")

        # Create the scraper
        scraper = ScraperFactory.get_scraper(bookmaker, sport, output_dir)

        # Load the events from the JSON file
        events = _load_events_for_bookmaker(bookmaker, sport, output_dir)
        scraper.events_data = events

        # Scrape detailed odds for the matched events
        odds = scraper.scrape_odds(event_ids)
        if odds:
            # Add bookmaker name to each odds entry
            for odd in odds:
                odd['Bookmaker'] = bookmaker
            all_odds.extend(odds)
            print(f"Scraped {len(odds)} detailed odds for {bookmaker}.")
        else:
            print(f"No odds found for {bookmaker}.")

    return all_odds


def _group_odds_by_event(all_odds: List[Dict[str, Any]]) -> Dict[str, List[Dict[str, Any]]]:
    """
    Group odds by event name

    Args:
        all_odds: List of odds entries

    Returns:
        Dictionary with event names as keys and lists of odds entries as values
    """
    grouped_odds = {}

    for odds_entry in all_odds:
        event_name = odds_entry.get('Name')
        if not event_name:
            continue

        # Normalize the event name to handle slight variations
        normalized_name = event_name.lower().strip()

        if normalized_name not in grouped_odds:
            grouped_odds[normalized_name] = []

        grouped_odds[normalized_name].append(odds_entry)

    return grouped_odds


def scrape_odds_for_matched_events(matcher: EventMatcher, bookmakers: List[str], sport: str,
                                  output_dir: str, use_async: bool = True) -> Dict[str, Any]:
    """
    Scrape detailed odds for matched events

    Args:
        matcher: EventMatcher with matched events
        bookmakers: List of bookmakers to scrape
        sport: Sport to scrape
        output_dir: Output directory
        use_async: Whether to use async scrapers

    Returns:
        Dictionary with results
    """
    print("\n=== STEP 3: SCRAPING DETAILED ODDS FOR MATCHED EVENTS ===\n")

    start_time = time.time()

    # Scrape odds using either async or sync method
    if use_async:
        # Run the async function in an event loop
        all_odds = asyncio.run(_scrape_odds_async(matcher, bookmakers, sport, output_dir))
    else:
        all_odds = _scrape_odds_sync(matcher, bookmakers, sport, output_dir)

    # Group odds by event ID
    grouped_odds = _group_odds_by_event(all_odds)

    # Save detailed odds to file
    odds_path = os.path.join(output_dir, f"detailed_odds_{time.strftime('%Y%m%d_%H%M%S')}.json")
    with open(odds_path, 'w') as f:
        json.dump(grouped_odds, f, indent=2)

    duration = time.time() - start_time
    print(f"\nScraped {len(all_odds)} detailed odds grouped by {len(grouped_odds)} event names in {duration:.2f} seconds.")
    print(f"Detailed odds saved to: {odds_path}")

    return {
        "odds_count": len(all_odds),
        "events_count": len(grouped_odds),
        "odds_path": odds_path
    }
