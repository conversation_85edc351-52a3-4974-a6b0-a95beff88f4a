#!/usr/bin/env python3
"""
Script to match events across bookmakers and create a comparison table.
"""

import os
import sys
import json
import argparse
import pandas as pd
import time
from datetime import datetime
from typing import Dict, List, Any
from tqdm import tqdm
from collections import defaultdict

from models import EventMatcher


def load_events_from_directory(directory: str) -> Dict[str, List[Dict[str, Any]]]:
    """
    Load events from JSON files in the specified directory structure

    Args:
        directory: Base directory for event data

    Returns:
        Dictionary with bookmaker name as key and list of events as value
    """
    events_by_bookmaker = {}

    try:
        # List all subdirectories (bookmakers)
        bookmakers = [d for d in os.listdir(directory)
                    if os.path.isdir(os.path.join(directory, d))]

        print(f"Found {len(bookmakers)} potential bookmaker directories")

        # Use a progress bar for loading events
        with tqdm(total=len(bookmakers), desc="Loading event data") as pbar:
            for bookmaker in bookmakers:
                bookmaker_dir = os.path.join(directory, bookmaker, 'football')
                events_file = os.path.join(bookmaker_dir, 'all_football_events.json')

                if not os.path.exists(events_file):
                    pbar.write(f"No events file found for {bookmaker}")
                    pbar.update(1)
                    continue

                try:
                    with open(events_file, 'r') as f:
                        events_data = json.load(f)

                    # Validate data
                    if not isinstance(events_data, list):
                        pbar.write(f"Invalid events data format for {bookmaker}")
                        pbar.update(1)
                        continue

                    if events_data:
                        events_by_bookmaker[bookmaker] = events_data
                        pbar.write(f"Loaded {len(events_data)} events for {bookmaker}")
                    pbar.update(1)
                except (json.JSONDecodeError, IOError) as e:
                    pbar.write(f"Error loading events for {bookmaker}: {e}")
                    pbar.update(1)

    except Exception as e:
        print(f"Error loading events: {e}")

    return events_by_bookmaker


def match_events(events_by_bookmaker: Dict[str, List[Dict[str, Any]]],
                name_similarity: float = 0.85,
                team_similarity: float = 0.8,
                time_window: int = 60) -> EventMatcher:
    """
    Match events across bookmakers

    Args:
        events_by_bookmaker: Dictionary with bookmaker name as key and list of events as value
        name_similarity: Threshold for event name similarity (0-1)
        team_similarity: Threshold for team name similarity (0-1)
        time_window: Time window in minutes to consider events as potential matches

    Returns:
        EventMatcher instance with matched events
    """
    # Create matcher with specified parameters
    matcher = EventMatcher(
        name_similarity_threshold=name_similarity,
        team_similarity_threshold=team_similarity,
        time_window_minutes=time_window
    )

    # Match events with progress bars
    print(f"Matching with parameters: name_similarity={name_similarity}, team_similarity={team_similarity}, time_window={time_window}min")
    matcher.match_events(events_by_bookmaker)

    return matcher


def save_matched_events(matcher: EventMatcher, output_path: str) -> None:
    """
    Save matched events to a JSON file

    Args:
        matcher: EventMatcher with matched events
        output_path: Path to save the JSON file
    """
    print("\nExporting matched events to JSON...")

    start_time = time.time()

    # Get events with progress tracking
    with tqdm(total=1, desc="Preparing events data") as pbar:
        # Export matched events to dictionary format
        data = matcher.export_to_dict_list()
        pbar.update(1)

    # Save to file with progress tracking
    with tqdm(total=1, desc=f"Writing {len(data)} events to {output_path}") as pbar:
        with open(output_path, 'w') as f:
            json.dump(data, f, indent=2)
        pbar.update(1)

    duration = time.time() - start_time
    print(f"Saved {len(data)} matched events to {output_path} in {duration:.2f} seconds")
    print(f"File size: {os.path.getsize(output_path) / (1024*1024):.2f} MB")


def save_comparison_table(matcher: EventMatcher, output_path: str) -> None:
    """
    Save comparison table to CSV file

    Args:
        matcher: EventMatcher with matched events
        output_path: Path to save the CSV file
    """
    print("\nGenerating CSV comparison table...")

    start_time = time.time()

    # Progress steps
    total_steps = 3
    with tqdm(total=total_steps, desc="Creating comparison table") as pbar:
        # Step 1: Export data
        pbar.set_description("Preparing comparison data")
        data = matcher.export_to_comparison_table()
        pbar.update(1)

        # Step 2: Convert to DataFrame
        pbar.set_description("Converting to DataFrame")
        df = pd.DataFrame(data)
        pbar.update(1)

        # Step 3: Save to CSV
        pbar.set_description(f"Saving CSV with {len(data)} rows")
        df.to_csv(output_path, index=False)
        pbar.update(1)

    duration = time.time() - start_time
    print(f"Saved comparison table with {len(data)} rows to {output_path} in {duration:.2f} seconds")
    print(f"File size: {os.path.getsize(output_path) / (1024*1024):.2f} MB")


def save_comparison_excel(matcher: EventMatcher, output_path: str) -> None:
    """
    Save comparison table to Excel file with formatting

    Args:
        matcher: EventMatcher with matched events
        output_path: Path to save the Excel file
    """
    print("\nGenerating Excel comparison table with formatting...")

    start_time = time.time()

    # Define progress steps
    total_steps = 7  # Updated to include summary sheet
    with tqdm(total=total_steps, desc="Creating Excel report") as pbar:
        # Step 1: Export data
        pbar.set_description("Preparing comparison data")
        data = matcher.export_to_comparison_table()
        pbar.update(1)

        if not data:
            pbar.write("No matched events to save")
            return

        # Step 2: Create DataFrame
        pbar.set_description("Converting to DataFrame")
        df = pd.DataFrame(data)
        writer = pd.ExcelWriter(output_path, engine='xlsxwriter')
        pbar.update(1)

        # Step 3: Write to Excel
        pbar.set_description(f"Writing {len(data)} rows to Excel")
        df.to_excel(writer, sheet_name='Matched Events', index=False)
        pbar.update(1)

        # Step 4: Format headers and set column widths
        pbar.set_description("Formatting headers and columns")
        workbook = writer.book
        worksheet = writer.sheets['Matched Events']

        # Create header format
        header_format = workbook.add_format({
            'bold': True,
            'text_wrap': True,
            'valign': 'top',
            'fg_color': '#D7E4BC',
            'border': 1
        })

        # Create odds format
        odds_format = workbook.add_format({
            'num_format': '0.00',
            'border': 1
        })

        # Create formats for best odds (highlight in green)
        best_odds_format = workbook.add_format({
            'num_format': '0.00',
            'border': 1,
            'bg_color': '#C6EFCE',  # Light green
            'font_color': '#006100'  # Dark green
        })

        # Removed arbitrage format

        # Value percentage formats - from red (bad) to green (good)
        value_bad_format = workbook.add_format({
            'num_format': '0.00%',
            'bg_color': '#FFC7CE',  # Light red
            'font_color': '#9C0006'  # Dark red
        })

        value_medium_format = workbook.add_format({
            'num_format': '0.00%',
            'bg_color': '#FFEB9C',  # Light yellow
            'font_color': '#9C6500'  # Dark yellow
        })

        value_good_format = workbook.add_format({
            'num_format': '0.00%',
            'bg_color': '#C6EFCE',  # Light green
            'font_color': '#006100'  # Dark green
        })

        # Apply header format and set column widths
        for col_num, value in enumerate(df.columns.values):
            worksheet.write(0, col_num, value, header_format)

        # Set column widths
        worksheet.set_column('A:A', 10)  # match_id
        worksheet.set_column('B:B', 30)  # name
        worksheet.set_column('C:D', 20)  # home, away
        worksheet.set_column('E:E', 18)  # start_time
        worksheet.set_column('F:F', 15)  # country
        worksheet.set_column('G:G', 20)  # competition
        worksheet.set_column('H:H', 30)  # bookmakers
        pbar.update(1)

        # Step 5: Format odds columns and IDs
        pbar.set_description("Formatting odds columns and IDs")
        # Identify all odds columns by finding columns that belong to bookmakers
        bookmaker_prefixes = set()
        for col in df.columns:
            if '_' in col:
                prefix = col.split('_')[0]
                if prefix not in ['best', 'match']:
                    bookmaker_prefixes.add(prefix)

        # Now find all odds columns for these bookmakers
        odds_columns = []
        for prefix in bookmaker_prefixes:
            # Add standard 1X2 odds
            for suffix in ['_1', '_X', '_2']:
                col = f"{prefix}{suffix}"
                if col in df.columns:
                    odds_columns.append(col)

            # Add any other odds columns that aren't IDs or values
            for col in df.columns:
                if col.startswith(prefix + '_') and not col.endswith('_id') and not col.endswith('_value'):
                    if col not in odds_columns:
                        odds_columns.append(col)

        # Find ID columns
        id_columns = [col for col in df.columns if col.endswith('_id')]

        # Find value columns
        value_columns = [col for col in df.columns if col.endswith('_value')]

        # Find best odds columns
        best_odds_columns = [col for col in df.columns if col.startswith('best_') and col.endswith('_odds')]
        best_bookmaker_columns = [col for col in df.columns if col.startswith('best_') and col.endswith('_bookmaker')]

        # Removed arbitrage columns

        # Create an ID column format
        id_format = workbook.add_format({
            'num_format': '@',  # Text format
            'border': 1,
            'align': 'left'
        })

        # Format ID columns
        for col_name in id_columns:
            if col_name in df.columns:
                col_idx = df.columns.get_loc(col_name)
                worksheet.set_column(col_idx, col_idx, 15, id_format)

                # Add conditional formatting to highlight IDs
                # Use light yellow background for IDs
                worksheet.conditional_format(1, col_idx, len(df), col_idx, {
                    'type': 'no_blanks',
                    'format': workbook.add_format({
                        'bg_color': '#FFFFCC',
                        'border': 1
                    })
                })

        # Format all best odds columns
        for col_name in best_odds_columns:
            if col_name in df.columns:
                col_idx = df.columns.get_loc(col_name)
                worksheet.set_column(col_idx, col_idx, 10, best_odds_format)

        # Format all best bookmaker columns
        for col_name in best_bookmaker_columns:
            if col_name in df.columns:
                col_idx = df.columns.get_loc(col_name)
                worksheet.set_column(col_idx, col_idx, 15)

        # Removed arbitrage formatting

        # Group by bookmaker
        bookmakers = set([col.split('_')[0] for col in odds_columns])

        for bookmaker in bookmakers:
            # Get all columns for this bookmaker (ID + odds)
            col_indices = []

            # Check for ID column first
            id_col_name = f"{bookmaker}_id"
            if id_col_name in df.columns:
                id_col_idx = df.columns.get_loc(id_col_name)
                col_indices.append(id_col_idx)

            # Collect all columns for this bookmaker
            bookmaker_cols = []

            # Add odds columns (including standard and detailed markets)
            for col in odds_columns:
                if col.startswith(f"{bookmaker}_"):
                    bookmaker_cols.append(col)
                    col_idx = df.columns.get_loc(col)
                    col_indices.append(col_idx)

            # Add value percentage columns
            for col in value_columns:
                if col.startswith(f"{bookmaker}_"):
                    bookmaker_cols.append(col)
                    col_idx = df.columns.get_loc(col)
                    col_indices.append(col_idx)

            # Format all the columns
            for col_name in bookmaker_cols:
                col_idx = df.columns.get_loc(col_name)

                # Different formatting based on column type
                if col_name.endswith("_value"):
                    # Value percentage column
                    worksheet.set_column(col_idx, col_idx, 10)

                    # Conditional formatting for value ranges
                    worksheet.conditional_format(1, col_idx, len(df), col_idx, {
                        'type': 'cell',
                        'criteria': '<',
                        'value': 90,
                        'format': value_bad_format
                    })

                    worksheet.conditional_format(1, col_idx, len(df), col_idx, {
                        'type': 'cell',
                        'criteria': 'between',
                        'minimum': 90,
                        'maximum': 98,
                        'format': value_medium_format
                    })

                    worksheet.conditional_format(1, col_idx, len(df), col_idx, {
                        'type': 'cell',
                        'criteria': '>=',
                        'value': 98,
                        'format': value_good_format
                    })
                else:
                    # Regular odds column
                    worksheet.set_column(col_idx, col_idx, 10, odds_format)

                    # Extract the market type from the column name
                    market = col_name[len(bookmaker)+1:]

                    # Add conditional formatting to highlight when this is the best odds
                    best_bookie_col = f"best_{market}_bookmaker"
                    if best_bookie_col in df.columns:
                        worksheet.conditional_format(1, col_idx, len(df), col_idx, {
                            'type': 'formula',
                            'criteria': f'=$%s="{bookmaker}"' % (chr(65 + df.columns.get_loc(best_bookie_col))),
                            'format': best_odds_format
                        })

            # Add a border to separate bookmaker groups if we have multiple columns
            if len(col_indices) > 0:
                # Get the max column index
                max_col = max(col_indices)

                # Add a right border to the last column in the bookmaker group
                if max_col < len(df.columns) - 1:  # If not the last column in the sheet
                    # Get current format for the last column
                    col_name = df.columns[max_col]
                    if col_name.endswith('_id'):
                        # For ID columns, combine with ID format
                        border_format = workbook.add_format({
                            'num_format': '@',  # Text format
                            'border': 1,
                            'align': 'left',
                            'right': 2  # Add thick right border
                        })
                        worksheet.set_column(max_col, max_col, 15, border_format)
                    elif any(col_name.endswith(suffix) for suffix in ['_1', '_X', '_2']):
                        # For odds columns, combine with odds format
                        border_format = workbook.add_format({
                            'num_format': '0.00',
                            'border': 1,
                            'right': 2  # Add thick right border
                        })
                        worksheet.set_column(max_col, max_col, 10, border_format)
                    else:
                        # Default case
                        right_border_format = workbook.add_format({'right': 2})
                        worksheet.set_column(max_col, max_col, None, right_border_format)
        pbar.update(1)

        # Step 6: Create summary sheet with analysis
        pbar.set_description("Creating summary analysis sheet")

        # Add a second sheet with summary statistics
        summary_sheet = workbook.add_worksheet('Summary')

        # Write title
        summary_sheet.write(0, 0, 'Summary Statistics', header_format)
        summary_sheet.write(0, 1, f'Generated on {datetime.now().strftime("%Y-%m-%d %H:%M")}')

        # Removed arbitrage opportunities section
        row = 2

        # Calculate bookmaker value statistics
        bookmaker_value_sum = defaultdict(float)
        bookmaker_count = defaultdict(int)
        best_odds_count = defaultdict(lambda: defaultdict(int))

        for event in data:
            # Process value percentages
            for key in event.keys():
                if key.endswith("_value"):
                    bookmaker = key.split("_")[0]
                    if event[key] is not None:
                        bookmaker_value_sum[bookmaker] += event[key]
                        bookmaker_count[bookmaker] += 1

            # Process best odds counts
            for outcome in ['1', 'X', '2']:
                bookmaker = event.get(f"best_{outcome}_bookmaker")
                if bookmaker:
                    best_odds_count[bookmaker][outcome] += 1

        # Calculate average values
        bookmaker_avg_value = {}
        for bookmaker, total in bookmaker_value_sum.items():
            count = bookmaker_count[bookmaker]
            if count > 0:
                bookmaker_avg_value[bookmaker] = total / count

        # Add bookmaker analysis
        row += 2
        summary_sheet.write(row, 0, 'Bookmaker Value Analysis', workbook.add_format({'bold': True}))
        row += 1

        # Headers
        summary_sheet.write(row, 0, 'Bookmaker')
        summary_sheet.write(row, 1, 'Avg Value %')
        summary_sheet.write(row, 2, 'Home Best')
        summary_sheet.write(row, 3, 'Draw Best')
        summary_sheet.write(row, 4, 'Away Best')
        summary_sheet.write(row, 5, 'Total Best')
        row += 1

        # Fill bookmaker analysis data
        bookmaker_totals = {}
        for bookmaker, outcomes in best_odds_count.items():
            total = sum(outcomes.values())
            bookmaker_totals[bookmaker] = total

        for bookmaker, avg_value in sorted(bookmaker_avg_value.items(), key=lambda x: x[1], reverse=True):
            outcomes = best_odds_count[bookmaker]
            total = bookmaker_totals.get(bookmaker, 0)

            summary_sheet.write(row, 0, bookmaker)
            summary_sheet.write(row, 1, avg_value)
            summary_sheet.write(row, 2, outcomes['1'])
            summary_sheet.write(row, 3, outcomes['X'])
            summary_sheet.write(row, 4, outcomes['2'])
            summary_sheet.write(row, 5, total)
            row += 1

        # Set column widths for summary sheet
        summary_sheet.set_column('A:A', 30)  # Event name/bookmaker
        summary_sheet.set_column('B:B', 20)  # Start time/Avg value
        summary_sheet.set_column('C:C', 10)  # Profit/Home best
        summary_sheet.set_column('D:D', 10)  # Home odds/Draw best
        summary_sheet.set_column('E:E', 15)  # Home bookmaker/Away best
        summary_sheet.set_column('F:F', 10)  # Draw odds/Total best
        summary_sheet.set_column('G:G', 15)  # Draw bookmaker
        summary_sheet.set_column('H:H', 10)  # Away odds
        summary_sheet.set_column('I:I', 15)  # Away bookmaker

        pbar.update(1)

        # Step 7: Save and close
        pbar.set_description("Saving Excel workbook")
        writer.close()
        pbar.update(1)

    # Report completion
    duration = time.time() - start_time
    filesize_mb = os.path.getsize(output_path) / (1024*1024)
    print(f"Saved Excel comparison with {len(data)} rows to {output_path} in {duration:.2f} seconds")
    print(f"File size: {filesize_mb:.2f} MB")


def main():
    """Main function"""
    parser = argparse.ArgumentParser(description='Match events across bookmakers')
    parser.add_argument('--input-dir', '-i', default='output', help='Input directory (default: output)')
    parser.add_argument('--output-dir', '-o', default='matched', help='Output directory (default: matched)')
    parser.add_argument('--name-similarity', '-n', type=float, default=0.85,
                        help='Name similarity threshold (default: 0.85)')
    parser.add_argument('--team-similarity', '-t', type=float, default=0.8,
                        help='Team similarity threshold (default: 0.8)')
    parser.add_argument('--time-window', '-w', type=int, default=120,
                        help='Time window in minutes (default: 120)')
    args = parser.parse_args()

    # Ensure output directory exists
    os.makedirs(args.output_dir, exist_ok=True)

    # Load events
    print(f"Loading events from {args.input_dir}...")
    events_by_bookmaker = load_events_from_directory(args.input_dir)

    if not events_by_bookmaker:
        print("No events loaded. Exiting.")
        sys.exit(1)

    total_events = sum(len(events) for events in events_by_bookmaker.values())
    print(f"Loaded {total_events} events from {len(events_by_bookmaker)} bookmakers")

    # Match events with detailed timing
    print("\nMatching events across bookmakers...")
    print(f"Parameters: name_similarity={args.name_similarity}, team_similarity={args.team_similarity}, time_window={args.time_window} minutes")

    start_time = time.time()

    # Create and configure matcher
    matcher = EventMatcher(
        name_similarity_threshold=args.name_similarity,
        team_similarity_threshold=args.team_similarity,
        time_window_minutes=args.time_window
    )

    # Match events with progress tracking
    matched_events = matcher.match_events(events_by_bookmaker)

    # Calculate and display statistics
    total_matches = len(matched_events)
    multi_bookmaker_matches = sum(1 for event in matched_events if len(event.bookmaker_events) > 1)
    duration = time.time() - start_time

    print(f"\nMatching Summary:")
    print(f"- Found {total_matches} total matched events in {duration:.2f} seconds")
    print(f"- {multi_bookmaker_matches} events matched across multiple bookmakers ({(multi_bookmaker_matches/total_matches)*100:.1f}%)")

    # Print bookmaker distribution
    print("\nEvents per bookmaker:")
    bookmaker_counts = defaultdict(int)
    for event in matched_events:
        for bookmaker in event.bookmaker_events.keys():
            bookmaker_counts[bookmaker] += 1

    for bookmaker, count in sorted(bookmaker_counts.items(), key=lambda x: x[1], reverse=True):
        print(f"  - {bookmaker}: {count} events")

    # Generate timestamp for filenames
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")

    # Save matched events to JSON
    json_path = os.path.join(args.output_dir, f"matched_events_{timestamp}.json")
    save_matched_events(matcher, json_path)

    # Save comparison table to CSV
    csv_path = os.path.join(args.output_dir, f"comparison_table_{timestamp}.csv")
    save_comparison_table(matcher, csv_path)

    # Save comparison table to Excel with formatting
    excel_path = os.path.join(args.output_dir, f"comparison_table_{timestamp}.xlsx")
    save_comparison_excel(matcher, excel_path)

    # Export comparison table data
    table_data = matcher.export_to_comparison_table()

    # Identify all market types in the data
    all_market_types = set()
    detailed_market_types = set()

    # Find all market types
    for event in table_data:
        for key in event.keys():
            if '_' in key and key.split('_')[0] not in ['best', 'match']:
                parts = key.split('_')
                # If it's a standard 1X2 market
                if parts[1] in ['1', 'X', '2'] and len(parts) == 2:
                    all_market_types.add(parts[1])
                # If it's a detailed market
                elif len(parts) > 2 and not key.endswith('_id') and not key.endswith('_value'):
                    market = '_'.join(parts[1:])
                    detailed_market_types.add(market)

    print(f"\n=== DETECTED MARKET TYPES ===")
    print(f"Standard markets: {', '.join(sorted(all_market_types))}")
    if detailed_market_types:
        print(f"Detailed markets: {', '.join(sorted(detailed_market_types))}")
    else:
        print("No detailed markets found in data")

    print("\nEvent matching and analysis complete!")


if __name__ == "__main__":
    main()