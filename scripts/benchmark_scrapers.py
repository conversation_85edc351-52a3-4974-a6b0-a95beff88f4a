#!/usr/bin/env python3
"""
Benchmark script to compare performance of async vs. sync scrapers
"""

import time
import asyncio
from scrapers.core.scraper_factory import ScraperFactory

def run_sync_scraper(bookie_name):
    """Run a synchronous scraper and measure time"""
    print(f"Running synchronous {bookie_name} scraper...")
    
    # Get sync scraper
    scraper = ScraperFactory.get_scraper(bookie_name)
    
    # Measure time for basic events
    start_time = time.time()
    events = scraper.scrape_events()
    basic_time = time.time() - start_time
    print(f"- Events scraped: {len(events)} in {basic_time:.2f} seconds")
    
    # Take a subset for detailed odds to keep test time reasonable
    test_events = events[:5]
    
    # Measure time for detailed odds
    start_time = time.time()
    odds = scraper.scrape_odds(test_events)
    detailed_time = time.time() - start_time
    print(f"- Detailed odds scraped for 5 events in {detailed_time:.2f} seconds")
    
    return {
        "events_count": len(events),
        "events_time": basic_time,
        "odds_time": detailed_time,
        "events_per_second": len(events) / basic_time if basic_time > 0 else 0,
        "odds_per_second": len(test_events) / detailed_time if detailed_time > 0 else 0
    }

async def run_async_scraper(bookie_name):
    """Run an asynchronous scraper and measure time"""
    print(f"Running asynchronous {bookie_name} scraper...")
    
    # Get async scraper
    scraper = ScraperFactory.get_async_scraper(bookie_name)
    
    # Measure time for basic events
    start_time = time.time()
    events = await scraper.scrape_events()
    basic_time = time.time() - start_time
    print(f"- Events scraped: {len(events)} in {basic_time:.2f} seconds")
    
    # Take a subset for detailed odds to keep test time reasonable
    test_events = events[:5]
    
    # Measure time for detailed odds
    start_time = time.time()
    odds = await scraper.scrape_odds(test_events)
    detailed_time = time.time() - start_time
    print(f"- Detailed odds scraped for 5 events in {detailed_time:.2f} seconds")
    
    return {
        "events_count": len(events),
        "events_time": basic_time,
        "odds_time": detailed_time,
        "events_per_second": len(events) / basic_time if basic_time > 0 else 0,
        "odds_per_second": len(test_events) / detailed_time if detailed_time > 0 else 0
    }

async def main_async():
    """Main async function"""
    results = {}
    
    # Get available async bookmakers
    async_bookmakers = ScraperFactory.get_available_async_bookmakers()
    
    if not async_bookmakers:
        print("No async bookmakers available")
        return
    
    # Run benchmarks for each async bookmaker
    for bookie in async_bookmakers:
        print("\n" + "="*50)
        print(f"BENCHMARKING {bookie.upper()}")
        print("="*50)
        
        # Run synchronous version
        sync_results = run_sync_scraper(bookie)
        
        # Run asynchronous version
        async_results = await run_async_scraper(bookie)
        
        # Calculate improvements
        events_speedup = async_results["events_per_second"] / sync_results["events_per_second"] if sync_results["events_per_second"] > 0 else float('inf')
        odds_speedup = async_results["odds_per_second"] / sync_results["odds_per_second"] if sync_results["odds_per_second"] > 0 else float('inf')
        
        # Print comparison
        print("\nCOMPARISON:")
        print(f"- Events scraping speedup: {events_speedup:.2f}x")
        print(f"- Detailed odds scraping speedup: {odds_speedup:.2f}x")
        
        # Save results
        results[bookie] = {
            "sync": sync_results,
            "async": async_results,
            "events_speedup": events_speedup,
            "odds_speedup": odds_speedup
        }
    
    return results

def main():
    """Entry point"""
    asyncio.run(main_async())

if __name__ == "__main__":
    main()