from dataclasses import dataclass, field
from typing import Dict, Any, Optional, List


@dataclass
class SportEvent:
    """Class representing a sports event with odds data"""

    ID: str
    Name: str
    Home: str
    Away: str
    Start: str
    HomeTeamID: Optional[str] = None
    AwayTeamID: Optional[str] = None
    CountryID: Optional[str] = None
    Country: Optional[str] = None
    CompetitionID: Optional[str] = None
    Competition: Optional[str] = None
    # Removed odds fields

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'SportEvent':
        """Create an event from a dictionary"""
        event = cls(
            ID=data.get("ID"),
            Name=data.get("Name"),
            Home=data.get("Home"),
            Away=data.get("Away"),
            Start=data.get("Start"),
            HomeTeamID=data.get("HomeTeamID"),
            AwayTeamID=data.get("AwayTeamID"),
            CountryID=data.get("CountryID"),
            Country=data.get("Country"),
            CompetitionID=data.get("CompetitionID"),
            Competition=data.get("Competition")
        )

        # Removed additional odds

        return event

    def to_dict(self) -> Dict[str, Any]:
        """Convert event to dictionary"""
        result = {
            "ID": self.ID,
            "Name": self.Name,
            "HomeTeamID": self.HomeTeamID,
            "Home": self.Home,
            "AwayTeamID": self.AwayTeamID,
            "Away": self.Away,
            "CountryID": self.CountryID,
            "Country": self.Country,
            "CompetitionID": self.CompetitionID,
            "Competition": self.Competition,
            "Start": self.Start
        }

        return result


@dataclass
class EventCollection:
    """Collection of sports events"""

    events: List[SportEvent] = field(default_factory=list)

    def add_event(self, event: SportEvent) -> None:
        """Add an event to the collection"""
        self.events.append(event)

    def to_dict_list(self) -> List[Dict[str, Any]]:
        """Convert events to list of dictionaries"""
        return [event.to_dict() for event in self.events]