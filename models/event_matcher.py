import re
import uuid
import time
import datetime
import dateutil.parser
from typing import Dict, List, Any, Optional, Tuple
from difflib import SequenceMatcher
import pytz
from tqdm import tqdm
from collections import defaultdict

from models.event import SportEvent


class MatchedEvent:
    """Class representing a matched event across different bookmakers"""

    def __init__(self,
                 match_id: str = None,
                 name: Optional[str] = None,
                 home: Optional[str] = None,
                 away: Optional[str] = None,
                 country: Optional[str] = None,
                 competition: Optional[str] = None,
                 start_time: Optional[str] = None):
        """
        Initialize a matched event

        Args:
            match_id: Unique identifier for the match (generates UUID if None)
            name: Standardized event name
            home: Standardized home team name
            away: Standardized away team name
            country: Standardized country name
            competition: Standardized competition name
            start_time: ISO 8601 formatted start time
        """
        self.match_id = match_id if match_id else str(uuid.uuid4())
        self.name = name
        self.home = home
        self.away = away
        self.country = country
        self.competition = competition
        self.start_time = start_time
        self.bookmaker_events: Dict[str, SportEvent] = {}

    def add_bookmaker_event(self, bookmaker: str, event: SportEvent) -> None:
        """
        Add an event from a specific bookmaker

        Args:
            bookmaker: Name of the bookmaker
            event: SportEvent instance
        """
        self.bookmaker_events[bookmaker] = event

        # Update normalized fields if not already set
        if not self.name and event.Name:
            self.name = event.Name

        if not self.home and event.Home:
            self.home = event.Home

        if not self.away and event.Away:
            self.away = event.Away

        if not self.country and event.Country:
            self.country = event.Country

        if not self.competition and event.Competition:
            self.competition = event.Competition

        if not self.start_time and event.Start:
            self.start_time = event.Start

    def get_bookmakers(self) -> List[str]:
        """Get list of bookmakers for this matched event"""
        return list(self.bookmaker_events.keys())

    def get_event_by_bookmaker(self, bookmaker: str) -> Optional[SportEvent]:
        """Get event data for a specific bookmaker"""
        return self.bookmaker_events.get(bookmaker)

    def to_dict(self) -> Dict[str, Any]:
        """Convert matched event to dictionary"""
        return {
            "match_id": self.match_id,
            "name": self.name,
            "home": self.home,
            "away": self.away,
            "country": self.country,
            "competition": self.competition,
            "start_time": self.start_time,
            "bookmakers": self.get_bookmakers(),
            "bookmaker_data": {
                bookie: event.to_dict()
                for bookie, event in self.bookmaker_events.items()
            }
        }


class EventMatcher:
    """Utility class for matching events across different bookmakers"""

    def __init__(self,
                 name_similarity_threshold: float = 0.85,
                 team_similarity_threshold: float = 0.8,
                 time_window_minutes: int = 60):
        """
        Initialize the event matcher

        Args:
            name_similarity_threshold: Threshold for event name similarity (0-1)
            team_similarity_threshold: Threshold for team name similarity (0-1)
            time_window_minutes: Time window in minutes to consider events as potential matches
        """
        self.name_similarity_threshold = name_similarity_threshold
        self.team_similarity_threshold = team_similarity_threshold
        self.time_window_minutes = time_window_minutes
        self.matched_events: List[MatchedEvent] = []
        self.timezone = pytz.timezone('Europe/Brussels')  # Default timezone

        # Add caches for better performance
        self.normalized_name_cache: Dict[str, str] = {}
        self.similarity_cache: Dict[Tuple[str, str], float] = {}
        self.time_normalization_cache: Dict[str, datetime.datetime] = {}

    def normalize_name(self, name: str) -> str:
        """
        Normalize a team or event name with caching

        Args:
            name: Original name

        Returns:
            Normalized name
        """
        if not name:
            return ""

        # Check cache first
        if name in self.normalized_name_cache:
            return self.normalized_name_cache[name]

        # Convert to lowercase
        normalized = name.lower()

        # Replace common separators with spaces
        normalized = re.sub(r'[-_./]', ' ', normalized)

        # Remove non-alphanumeric characters
        normalized = re.sub(r'[^\w\s]', '', normalized)

        # Remove multiple spaces
        normalized = re.sub(r'\s+', ' ', normalized)

        # Common team name variations
        normalized = normalized.replace('fc ', '').replace(' fc', '')
        normalized = normalized.replace('united', 'utd')
        normalized = normalized.replace('olympique', 'ol')
        normalized = normalized.replace('real', 'r')
        normalized = normalized.replace('atletico', 'atl')

        result = normalized.strip()

        # Store in cache
        self.normalized_name_cache[name] = result

        return result

    def calculate_similarity(self, str1: Optional[str], str2: Optional[str]) -> float:
        """
        Calculate string similarity using SequenceMatcher with caching

        Args:
            str1: First string
            str2: Second string

        Returns:
            Similarity score between 0 and 1
        """
        if not str1 or not str2:
            return 0

        # Create a cache key (using the smaller string first for consistency)
        if len(str1) <= len(str2):
            cache_key = (str1, str2)
        else:
            cache_key = (str2, str1)

        # Check cache first
        if cache_key in self.similarity_cache:
            return self.similarity_cache[cache_key]

        # Calculate similarity
        norm1 = self.normalize_name(str1)
        norm2 = self.normalize_name(str2)
        similarity = SequenceMatcher(None, norm1, norm2).ratio()

        # Store in cache
        self.similarity_cache[cache_key] = similarity

        return similarity

    def normalize_time(self, time_str: str) -> datetime.datetime:
        """
        Normalize a time string to a datetime object with timezone

        Args:
            time_str: Time string in any parseable format

        Returns:
            Normalized datetime with timezone
        """
        # Check cache first
        if time_str in self.time_normalization_cache:
            return self.time_normalization_cache[time_str]

        # Parse time string
        dt = dateutil.parser.parse(time_str)

        # Add timezone if not present
        if dt.tzinfo is None:
            dt = dt.replace(tzinfo=self.timezone)

        # Store in cache
        self.time_normalization_cache[time_str] = dt

        return dt

    def is_time_match(self, time1: str, time2: str) -> bool:
        """
        Check if two time strings are within the specified window

        Args:
            time1: First time string
            time2: Second time string

        Returns:
            True if times are within the window, False otherwise
        """
        if not time1 or not time2:
            return False

        # Create a cache key (using the smaller string first for consistency)
        cache_key = (time1, time2) if time1 <= time2 else (time2, time1)

        # Use a property-based cache key to avoid dictionary lookups
        cache_key = f"time_match:{cache_key[0]}:{cache_key[1]}"

        # Check memory attributes for cached result
        if hasattr(self, cache_key):
            return getattr(self, cache_key)

        try:
            dt1 = self.normalize_time(time1)
            dt2 = self.normalize_time(time2)
            delta = abs((dt1 - dt2).total_seconds() / 60)
            result = delta <= self.time_window_minutes

            # Cache the result
            setattr(self, cache_key, result)

            return result
        except (ValueError, TypeError):
            # Cache negative result
            setattr(self, cache_key, False)
            return False

    def find_best_match(self, event: SportEvent) -> Tuple[Optional[MatchedEvent], float]:
        """
        Find the best matching event among existing matched events

        Args:
            event: Event to match

        Returns:
            Tuple of (best matching event, match score)
        """
        best_match = None
        best_score = 0

        for matched_event in self.matched_events:
            # Calculate match score based on multiple factors

            # 1. Check if start times are within window
            if not self.is_time_match(matched_event.start_time, event.Start):
                continue

            # 2. Calculate team similarity scores
            home_similarity = self.calculate_similarity(matched_event.home, event.Home)
            away_similarity = self.calculate_similarity(matched_event.away, event.Away)

            # Check if teams might be swapped (home/away confusion)
            alt_home_similarity = self.calculate_similarity(matched_event.home, event.Away)
            alt_away_similarity = self.calculate_similarity(matched_event.away, event.Home)

            # Use the best team matching orientation
            if home_similarity + away_similarity >= alt_home_similarity + alt_away_similarity:
                team_score = (home_similarity + away_similarity) / 2
            else:
                team_score = (alt_home_similarity + alt_away_similarity) / 2

            # 3. Calculate event name similarity
            name_similarity = self.calculate_similarity(matched_event.name, event.Name)

            # 4. Country and competition bonus
            country_match = self.calculate_similarity(matched_event.country, event.Country) > 0.8 if matched_event.country and event.Country else 0
            competition_match = self.calculate_similarity(matched_event.competition, event.Competition) > 0.8 if matched_event.competition and event.Competition else 0

            context_bonus = (country_match + competition_match) * 0.1

            # Combined score with weights
            score = (team_score * 0.7) + (name_similarity * 0.2) + context_bonus

            # Update best match if score is higher
            if score > best_score and team_score >= self.team_similarity_threshold:
                best_match = matched_event
                best_score = score

        return best_match, best_score

    def create_time_index(self, events_by_bookmaker: Dict[str, List[Dict[str, Any]]]) -> Dict[datetime.date, List[Tuple[str, SportEvent]]]:
        """
        Create a time-based index of events to optimize matching

        Args:
            events_by_bookmaker: Dictionary with bookmaker name as key and list of events as value

        Returns:
            Dictionary with date as key and list of (bookmaker, event) tuples as value
        """
        time_index = defaultdict(list)

        # Get total count for progress bar
        total_events = sum(len(events_data) for events_data in events_by_bookmaker.values())

        # Process all events with a progress bar
        with tqdm(total=total_events, desc="Creating time index") as pbar:
            for bookmaker, events_data in events_by_bookmaker.items():
                for event_data in events_data:
                    # Convert to SportEvent object
                    event = SportEvent.from_dict(event_data)

                    # Get the date part of the start time
                    try:
                        dt = self.normalize_time(event.Start)
                        event_date = dt.date()

                        # Add to time index
                        time_index[event_date].append((bookmaker, event))
                    except (ValueError, TypeError):
                        # Skip events with invalid dates
                        pbar.write(f"Warning: Invalid date format in event {event.ID} - {event.Name}")

                    # Update progress bar
                    pbar.update(1)

        # Report statistics
        print(f"Created time index with {len(time_index)} unique dates")
        for date, events in sorted(time_index.items()):
            print(f"  - {date}: {len(events)} events")

        return time_index

    def match_events(self, events_by_bookmaker: Dict[str, List[Dict[str, Any]]]) -> List[MatchedEvent]:
        """
        Match events across different bookmakers

        Args:
            events_by_bookmaker: Dictionary with bookmaker name as key and list of events as value

        Returns:
            List of matched events
        """
        # Reset matched events
        self.matched_events = []

        # Track start time for performance reporting
        start_time = time.time()

        # Step 1: Create time-based index (with progress)
        print("Step 1/3: Creating time-based index...")
        time_index = self.create_time_index(events_by_bookmaker)

        # Step 2: Pre-process events by date for faster lookup
        print("Step 2/3: Pre-indexing events by date...")
        # Create a dict for faster lookup of matched events by date
        matched_events_by_date = defaultdict(list)

        # Get total count of events for progress bar
        total_events = sum(len(events) for events in events_by_bookmaker.values())
        total_dates = len(time_index)

        # Step 3: Process events using the optimized date index
        print(f"Step 3/3: Matching {total_events} events across {total_dates} dates...")

        # Process events using the time index with a progress bar
        with tqdm(total=total_events, desc="Matching events") as pbar:
            # Process each date
            for date, bookmaker_events in time_index.items():
                # Also consider events from adjacent dates (for overnight matches)
                adjacent_dates = [
                    date - datetime.timedelta(days=1),
                    date,
                    date + datetime.timedelta(days=1)
                ]

                # Process each event on this date
                for bookmaker, event in bookmaker_events:
                    # Check if this exact event (by ID) is already in any matched event
                    already_matched = False
                    for matched_event in self.matched_events:
                        # Check if this bookmaker is in the event
                        if bookmaker in matched_event.bookmaker_events:
                            # Check if the ID matches
                            if matched_event.bookmaker_events[bookmaker].ID == event.ID:
                                already_matched = True
                                break

                    # Skip if this exact event has already been matched
                    if already_matched:
                        pbar.update(1)
                        continue

                    # Build list of relevant matched events to check against
                    # Using a set to eliminate duplicates
                    relevant_matched_events = set()

                    # Add matched events from this and adjacent dates
                    for d in adjacent_dates:
                        for matched_event in matched_events_by_date.get(d, []):
                            # Skip if already has this bookmaker
                            if bookmaker not in matched_event.bookmaker_events:
                                relevant_matched_events.add(matched_event)

                    # Find best match among relevant matched events
                    best_match = None
                    best_score = 0

                    # Pre-filter by time window before detailed comparison
                    current_event_time = event.Start
                    filtered_events = []

                    for matched_event in relevant_matched_events:
                        # Skip if already has this bookmaker (double-check)
                        if bookmaker in matched_event.bookmaker_events:
                            continue

                        # Quick time check
                        if self.is_time_match(matched_event.start_time, current_event_time):
                            filtered_events.append(matched_event)

                    # Only process the time-filtered subset
                    for matched_event in filtered_events:
                        # Calculate team similarity scores - most expensive operation
                        home_similarity = self.calculate_similarity(matched_event.home, event.Home)
                        away_similarity = self.calculate_similarity(matched_event.away, event.Away)

                        # Check if teams might be swapped (home/away confusion)
                        # Only do this calculation if the straight comparison isn't good enough
                        if (home_similarity + away_similarity) / 2 < self.team_similarity_threshold:
                            alt_home_similarity = self.calculate_similarity(matched_event.home, event.Away)
                            alt_away_similarity = self.calculate_similarity(matched_event.away, event.Home)

                            # Use the best team matching orientation
                            if home_similarity + away_similarity >= alt_home_similarity + alt_away_similarity:
                                team_score = (home_similarity + away_similarity) / 2
                            else:
                                team_score = (alt_home_similarity + alt_away_similarity) / 2
                        else:
                            team_score = (home_similarity + away_similarity) / 2

                        # Skip early if team score doesn't meet threshold
                        if team_score < self.team_similarity_threshold:
                            continue

                        # Calculate event name similarity
                        name_similarity = self.calculate_similarity(matched_event.name, event.Name)

                        # Country and competition bonus - quick exact matches for speed
                        country_match = 1 if (matched_event.country and event.Country and
                                             matched_event.country.lower() == event.Country.lower()) else 0
                        competition_match = 1 if (matched_event.competition and event.Competition and
                                                matched_event.competition.lower() == event.Competition.lower()) else 0

                        # Only do similarity calculation if exact match fails but both values exist
                        if country_match == 0 and matched_event.country and event.Country:
                            country_match = 1 if self.calculate_similarity(matched_event.country, event.Country) > 0.8 else 0

                        if competition_match == 0 and matched_event.competition and event.Competition:
                            competition_match = 1 if self.calculate_similarity(matched_event.competition, event.Competition) > 0.8 else 0

                        context_bonus = (country_match + competition_match) * 0.1

                        # Combined score with weights
                        score = (team_score * 0.7) + (name_similarity * 0.2) + context_bonus

                        # Update best match if score is higher
                        if score > best_score:
                            best_match = matched_event
                            best_score = score

                    # Add event to best match or create new match
                    if best_match and best_score >= self.name_similarity_threshold:
                        # Add event to existing match
                        best_match.add_bookmaker_event(bookmaker, event)
                    else:
                        # Create new matched event
                        new_match = MatchedEvent(
                            name=event.Name,
                            home=event.Home,
                            away=event.Away,
                            country=event.Country,
                            competition=event.Competition,
                            start_time=event.Start
                        )
                        new_match.add_bookmaker_event(bookmaker, event)
                        self.matched_events.append(new_match)

                        # Add to date index for future searches
                        try:
                            event_date = self.normalize_time(event.Start).date()
                            matched_events_by_date[event_date].append(new_match)
                        except (ValueError, TypeError):
                            # Skip invalid dates for indexing
                            pass

                    # Update progress bar
                    pbar.update(1)

        # Report performance
        duration = time.time() - start_time
        events_per_second = total_events / duration if duration > 0 else 0
        print(f"\nMatched {total_events} events in {duration:.2f} seconds ({events_per_second:.2f} events/second)")
        print(f"Found {len(self.matched_events)} unique events, {sum(1 for e in self.matched_events if len(e.bookmaker_events) > 1)} with multiple bookmakers")

        return self.matched_events

    def export_to_dict_list(self) -> List[Dict[str, Any]]:
        """
        Export matched events to a list of dictionaries

        Returns:
            List of matched event dictionaries
        """
        # First, merge any duplicate events
        self.merge_duplicate_events()

        return [event.to_dict() for event in self.matched_events]

    def merge_duplicate_events(self) -> None:
        """
        Merge matched events that represent the same real event
        This handles cases where the same event appears multiple times in the data
        """
        if not self.matched_events:
            return

        print("Checking for duplicate events to merge...")

        # Group events by key attributes for matching
        event_groups = defaultdict(list)
        for event in self.matched_events:
            # Create a key based on home team, away team, start time, and country
            key = (
                self.normalize_name(event.home) if event.home else '',
                self.normalize_name(event.away) if event.away else '',
                event.start_time[:16] if event.start_time else '',  # Truncate to minute precision
                self.normalize_name(event.country) if event.country else ''
            )
            event_groups[key].append(event)

        # Find groups with more than one event (potential duplicates)
        duplicates = {k: v for k, v in event_groups.items() if len(v) > 1}

        if not duplicates:
            print("No duplicate events found.")
            return

        print(f"Found {len(duplicates)} potential duplicate event groups to merge")

        # Process each group of potential duplicates
        merged_count = 0
        events_to_remove = []

        # Create a progress bar for merging
        with tqdm(total=len(duplicates), desc="Merging duplicate events") as pbar:
            for key, events in duplicates.items():
                # Sort by number of bookmakers (descending) to keep the one with the most data
                events.sort(key=lambda e: len(e.bookmaker_events), reverse=True)

                # Use the first event as the primary one to keep
                primary_event = events[0]
                home_team, away_team = key[0], key[1]
                event_time = primary_event.start_time[:16] if primary_event.start_time else ''

                # Set descriptive progress message
                bookies_before = set(primary_event.bookmaker_events.keys())
                total_bookies = set(bookmaker for event in events for bookmaker in event.bookmaker_events.keys())
                pbar.set_description(f"Merging {home_team} vs {away_team} ({event_time}) - {len(bookies_before)}/{len(total_bookies)} bookmakers")

                # Merge others into it
                for secondary_event in events[1:]:
                    # Check if they're really the same event (same competition or high name similarity)
                    if (primary_event.competition and secondary_event.competition and
                        self.calculate_similarity(primary_event.competition, secondary_event.competition) > 0.8):

                        # Merge bookmakers from secondary into primary
                        for bookmaker, event in secondary_event.bookmaker_events.items():
                            # If this bookmaker is already in the primary event, make sure we're not losing data
                            if bookmaker in primary_event.bookmaker_events:
                                # If they have the same ID, it's the same event, no need to merge
                                if primary_event.bookmaker_events[bookmaker].ID == event.ID:
                                    continue

                                # Otherwise let's check if this is a truly different event from the same bookmaker
                                # If odds and key attributes are similar, we can merge the data
                                primary_event_data = primary_event.bookmaker_events[bookmaker]

                                # Log the IDs we're considering
                                pbar.write(f"  - Considering duplicate events from {bookmaker}: IDs {primary_event_data.ID} and {event.ID}")

                                # No odds comparison - just keep the first event we found
                                pbar.write(f"    - Keeping ID {primary_event_data.ID}")
                            else:
                                # New bookmaker, just add it
                                primary_event.add_bookmaker_event(bookmaker, event)

                        # Mark secondary for removal
                        events_to_remove.append(secondary_event)
                        merged_count += 1

                        # Update progress description to show current bookmakers
                        current_bookies = set(primary_event.bookmaker_events.keys())
                        pbar.set_description(f"Merging {home_team} vs {away_team} ({event_time}) - {len(current_bookies)}/{len(total_bookies)} bookmakers")

                # Update progress bar
                pbar.update(1)

        # Remove merged events
        if events_to_remove:
            self.matched_events = [event for event in self.matched_events if event not in events_to_remove]
            print(f"Merged {merged_count} duplicate events, resulting in {len(self.matched_events)} unique events")

    def export_to_comparison_table(self) -> List[Dict[str, Any]]:
        """
        Export matched events to a comparison table format

        Returns:
            List of dictionaries with event comparisons
        """
        # First, merge any duplicate events
        self.merge_duplicate_events()

        result = []

        for event in self.matched_events:
            if len(event.bookmaker_events) > 1:  # Only include events with multiple bookmakers
                comparison = {
                    "match_id": event.match_id,
                    "name": event.name,
                    "home": event.home,
                    "away": event.away,
                    "start_time": event.start_time,
                    "country": event.country,
                    "competition": event.competition,
                    "bookmakers": event.get_bookmakers()
                }

                # Add IDs for each bookmaker
                for bookmaker, evt in event.bookmaker_events.items():
                    # Add event ID from this bookmaker
                    comparison[f"{bookmaker}_id"] = evt.ID

                result.append(comparison)

        return result

    def _find_best_odds_for_all_markets(self, event: MatchedEvent, market_types: set) -> Dict[str, Dict[str, Any]]:
        """
        Placeholder for finding best odds (removed odds functionality)

        Args:
            event: MatchedEvent to analyze
            market_types: Set of all market types to check

        Returns:
            Empty dictionary (odds functionality removed)
        """
        # Return empty dictionary since odds functionality has been removed
        return {}

    def _find_best_odds(self, event: MatchedEvent) -> Dict[str, Dict[str, Any]]:
        """
        Placeholder for finding best odds (removed odds functionality)
        (Legacy method for backward compatibility)

        Args:
            event: MatchedEvent to analyze

        Returns:
            Empty dictionary (odds functionality removed)
        """
        # Return empty dictionary since odds functionality has been removed
        return {}

    def _find_market_combinations(self, market_types: set) -> Dict[str, List[str]]:
        """
        Find valid market combinations that could potentially form arbitrage opportunities

        Args:
            market_types: Set of all available market types

        Returns:
            Dictionary with combination names and lists of markets
        """
        combinations = {}

        # Standard 1X2 is already handled by default
        markets_list = list(market_types)

        # Look for common market combinations

        # Over/Under markets
        ou_markets = [m for m in markets_list if 'over' in m.lower() or 'under' in m.lower()]
        if len(ou_markets) >= 2:
            # Group by base (e.g., "goals_over_2.5" and "goals_under_2.5" share the base "goals_2.5")
            ou_groups = {}
            for market in ou_markets:
                # Try to extract the base by removing "over" or "under"
                base = None
                if 'over' in market.lower():
                    base = market.lower().replace('over', '').strip('_')
                elif 'under' in market.lower():
                    base = market.lower().replace('under', '').strip('_')

                if base:
                    if base not in ou_groups:
                        ou_groups[base] = []
                    ou_groups[base].append(market)

            # Add complete groups as combinations
            for base, markets in ou_groups.items():
                if len(markets) >= 2:  # Need at least over and under
                    combinations[f"ou_{base}"] = markets

        # Handicap markets
        handicap_markets = [m for m in markets_list if 'handicap' in m.lower()]
        if len(handicap_markets) >= 2:
            # Similar grouping as above
            combinations["handicap"] = handicap_markets

        # Both teams to score markets
        btts_markets = [m for m in markets_list if 'btts' in m.lower() or 'both_teams_to_score' in m.lower()]
        if len(btts_markets) >= 2:
            combinations["btts"] = btts_markets

        # Double chance markets (if they exist alongside 1X2)
        dc_markets = [m for m in markets_list if 'double_chance' in m.lower()]
        if len(dc_markets) >= 3:
            combinations["double_chance"] = dc_markets

        return combinations

    # Removed _check_arbitrage_for_markets method

    # Removed _check_arbitrage method