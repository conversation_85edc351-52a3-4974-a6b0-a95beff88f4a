"""
Module for normalizing odds data across different bookmakers.

This module provides classes and functions to normalize different odds
format conventions used by various bookmakers into a standard format
for easy comparison.
"""

import re
from typing import Dict, List, Any, Optional, Set, Tuple


class OddsNormalizer:
    """
    Class for normalizing odds from different bookmakers to a standard format.
    """
    
    # Standard market categories
    MARKET_CATEGORIES = {
        "1X2": ["match", "fulltime", "1x2", "win"],
        "DOUBLE_CHANCE": ["double chance", "double_chance", "dc"],
        "BTTS": ["both teams to score", "btts", "yes/no_both teams to score"],
        "OVER_UNDER": ["over/under", "total goals", "goals over/under", "over + / under -"],
        "ASIAN_HANDICAP": ["asian handicap", "handicap", "hc"],
        "CORRECT_SCORE": ["correct score", "cs"],
        "HALFTIME_FULLTIME": ["ht/ft", "half time/full time", "halftime/fulltime"],
        "ODD_EVEN": ["odd/even", "odd even"],
        "HALFTIME": ["1st half", "first half", "halftime"],
        "TEAM_GOALS": ["goals by", "team goals"],
        "DRAW_NO_BET": ["draw no bet", "dnb"],
        "FIRST_GOALSCORER": ["will score the 1st goal", "first goalscorer"]
    }
    
    # Standard outcome naming
    STANDARD_OUTCOMES = {
        # 1X2 outcomes
        "1": ["1", "home", "h"],
        "X": ["x", "draw", "tie"],
        "2": ["2", "away", "a"],
        
        # Yes/No outcomes
        "YES": ["yes", "y"],
        "NO": ["no", "n"],
        
        # Over/Under outcomes
        "OVER": ["over", "o", "over_"],
        "UNDER": ["under", "u", "under_"],
        
        # Odd/Even outcomes
        "ODD": ["odd", "uneven"],
        "EVEN": ["even"]
    }
    
    def __init__(self):
        """Initialize the normalizer with empty mappings."""
        # Maps raw market names to standard market names
        self.market_mappings = {}
        # Maps raw outcome names to standard outcome names
        self.outcome_mappings = {}
        # Maps odds keys to their normalized form
        self.key_mappings = {}
        # Keeps track of all detected market types
        self.detected_markets = set()
    
    def normalize_market_category(self, market_key: str) -> str:
        """
        Identify the market category from a raw market key.
        
        Args:
            market_key: Raw market key from a bookmaker
            
        Returns:
            Standardized market category
        """
        # Convert to lowercase for case-insensitive matching
        key_lower = market_key.lower()
        
        # Try to match against known market categories
        for category, patterns in self.MARKET_CATEGORIES.items():
            if any(pattern in key_lower for pattern in patterns):
                return category
        
        # If we can't categorize it, return the original
        return "OTHER"
    
    def normalize_market_name(self, market_key: str) -> str:
        """
        Normalize a market name to a standard format.
        
        Args:
            market_key: Raw market key from a bookmaker
            
        Returns:
            Standardized market name
        """
        # First, identify the market category
        category = self.normalize_market_category(market_key)
        
        # Handle specific market type normalization based on category
        key_lower = market_key.lower()
        
        if category == "1X2":
            return "MATCH_RESULT"
            
        elif category == "DOUBLE_CHANCE":
            return "DOUBLE_CHANCE"
            
        elif category == "BTTS":
            return "BOTH_TEAMS_TO_SCORE"
            
        elif category == "OVER_UNDER":
            # Extract the threshold value if present
            threshold_match = re.search(r'(\d+\.?\d*)', key_lower)
            if threshold_match:
                threshold = threshold_match.group(1)
                
                # Check if it's total goals or team-specific
                if "1st half" in key_lower or "first half" in key_lower or "halftime" in key_lower:
                    return f"OVER_UNDER_FIRST_HALF_{threshold}"
                elif "home" in key_lower or any(team in key_lower for team in ["by home", "home team"]):
                    return f"OVER_UNDER_HOME_{threshold}"
                elif "away" in key_lower or any(team in key_lower for team in ["by away", "away team"]):
                    return f"OVER_UNDER_AWAY_{threshold}"
                else:
                    return f"OVER_UNDER_{threshold}"
            else:
                return "OVER_UNDER"
                
        elif category == "ASIAN_HANDICAP":
            # Extract the handicap value if present
            handicap_match = re.search(r'(-?\d+\.?\d*)', key_lower)
            if handicap_match:
                handicap = handicap_match.group(1)
                return f"ASIAN_HANDICAP_{handicap}"
            else:
                return "ASIAN_HANDICAP"
                
        elif category == "CORRECT_SCORE":
            # Check if it has a specific score
            score_match = re.search(r'(\d+[:\-]\d+)', key_lower)
            if score_match:
                # Standardize the score format (e.g., replace ":" with "-")
                score = score_match.group(1).replace(':', '-')
                return f"CORRECT_SCORE_{score}"
            else:
                return "CORRECT_SCORE"
                
        elif category == "HALFTIME_FULLTIME":
            return "HALFTIME_FULLTIME"
            
        elif category == "ODD_EVEN":
            return "ODD_EVEN"
            
        elif category == "HALFTIME":
            if "who will win" in key_lower:
                return "HALFTIME_RESULT"
            elif "over/under" in key_lower or "total goals" in key_lower:
                threshold_match = re.search(r'(\d+\.?\d*)', key_lower)
                if threshold_match:
                    threshold = threshold_match.group(1)
                    return f"OVER_UNDER_FIRST_HALF_{threshold}"
                else:
                    return "OVER_UNDER_FIRST_HALF"
            else:
                return "HALFTIME_RESULT"  # Simplified to reduce variations
                
        elif category == "TEAM_GOALS":
            # Try to identify which team
            if "home" in key_lower or any(home_indicator in key_lower for home_indicator in ["team 1", "team1"]):
                return "HOME_TEAM_GOALS"
            elif "away" in key_lower or any(away_indicator in key_lower for away_indicator in ["team 2", "team2"]):
                return "AWAY_TEAM_GOALS"
            else:
                return "TEAM_GOALS"
                
        elif category == "DRAW_NO_BET":
            return "DRAW_NO_BET"
            
        elif category == "FIRST_GOALSCORER":
            return "FIRST_GOAL"
            
        else:
            # For unknown categories, try to make a more standardized name
            # First, simplify by removing numbers and special patterns
            simple_key = re.sub(r'\d+\.?\d*', '', key_lower)  # Remove numbers
            simple_key = re.sub(r'\([^)]*\)', '', simple_key)  # Remove parentheses
            
            # Remove common separators and spaces
            normalized = re.sub(r'[_\-\.\s]+', '_', simple_key.strip())
            # Remove any remaining special characters
            normalized = re.sub(r'[^\w_]', '', normalized)
            # Convert to uppercase for consistency
            
            # Clean up multiple underscores
            normalized = re.sub(r'_+', '_', normalized)
            # Remove leading/trailing underscores
            normalized = normalized.strip('_')
            
            if not normalized:
                return "OTHER"
                
            return normalized.upper()
    
    def normalize_outcome(self, market_category: str, outcome_key: str) -> str:
        """
        Normalize an outcome name to a standard format based on market category.
        
        Args:
            market_category: Normalized market category
            outcome_key: Raw outcome key from a bookmaker
            
        Returns:
            Standardized outcome name
        """
        if not outcome_key:
            return "UNKNOWN"
            
        # Convert to lowercase for case-insensitive matching
        key_lower = outcome_key.lower()
        
        # Match against standard outcomes
        for outcome, patterns in self.STANDARD_OUTCOMES.items():
            if any(pattern == key_lower or key_lower.startswith(pattern) for pattern in patterns):
                return outcome
        
        # Handle specific market types
        if market_category == "1X2":
            # Try to identify home, draw, away based on position in string
            if outcome_key == "1" or "home" in key_lower:
                return "1"
            elif outcome_key == "X" or "draw" in key_lower:
                return "X"
            elif outcome_key == "2" or "away" in key_lower:
                return "2"
                
        elif market_category == "DOUBLE_CHANCE":
            # Normalize double chance outcomes
            if "1x" in key_lower or "1/x" in key_lower or "home/draw" in key_lower:
                return "1X"
            elif "x2" in key_lower or "x/2" in key_lower or "draw/away" in key_lower:
                return "X2"
            elif "12" in key_lower or "1/2" in key_lower or "home/away" in key_lower:
                return "12"
                
        elif market_category == "BTTS":
            # Normalize both teams to score outcomes
            if "yes" in key_lower or "y" == key_lower:
                return "YES"
            elif "no" in key_lower or "n" == key_lower:
                return "NO"
                
        elif market_category.startswith("OVER_UNDER"):
            # Normalize over/under outcomes
            if "over" in key_lower or key_lower.startswith("o") or key_lower.startswith("+"):
                return "OVER"
            elif "under" in key_lower or key_lower.startswith("u") or key_lower.startswith("-"):
                return "UNDER"
                
        elif market_category.startswith("ASIAN_HANDICAP"):
            # Normalize Asian handicap outcomes
            # These often include the team name and handicap value
            # For now, just return a cleaned version of the outcome
            clean_outcome = re.sub(r'[^\w\s\-\+\.]+', '', key_lower)
            return clean_outcome.upper()
            
        elif market_category == "CORRECT_SCORE":
            # For correct score, just normalize the format (e.g., "2-1")
            score_match = re.search(r'(\d+[\-\:]?\d+)', key_lower)
            if score_match:
                # Standardize to the format "X-Y"
                score = score_match.group(1).replace(':', '-')
                return score
                
        elif market_category == "HALFTIME_FULLTIME":
            # Normalize HT/FT outcomes (e.g., "1/X", "X/2")
            # Common formats: "1/2", "Home/Away", "H/A"
            ht_ft_match = re.search(r'([12Xx])[/\-]([12Xx])', key_lower)
            if ht_ft_match:
                ht, ft = ht_ft_match.groups()
                # Standardize X vs x
                ht = "X" if ht.lower() == "x" else ht
                ft = "X" if ft.lower() == "x" else ft
                return f"{ht}/{ft}"
            else:
                # Try to handle other formats like "Home/Draw"
                parts = re.split(r'[/\-]', key_lower)
                if len(parts) == 2:
                    ht = "1" if "home" in parts[0] or "1" in parts[0] else "X" if "draw" in parts[0] or "x" in parts[0] else "2"
                    ft = "1" if "home" in parts[1] or "1" in parts[1] else "X" if "draw" in parts[1] or "x" in parts[1] else "2"
                    return f"{ht}/{ft}"
                    
        elif market_category == "ODD_EVEN":
            # Normalize odd/even outcomes
            if "odd" in key_lower:
                return "ODD"
            elif "even" in key_lower:
                return "EVEN"
                
        # For other cases, return a cleaned uppercase version
        clean_outcome = re.sub(r'[^\w\s\-\+\.]+', '', key_lower)
        return clean_outcome.upper()
    
    def parse_odds_key(self, key: str) -> Tuple[str, str, str]:
        """
        Parse an odds key into its components (market, outcome, team).
        
        Args:
            key: The odds key from a bookmaker
            
        Returns:
            Tuple of (market_name, outcome, team_name)
        """
        # Split the key into components
        parts = key.split('_')
        
        # Handle special case for simple 1/X/2 keys
        if key in ["1", "X", "2"]:
            return "MATCH_RESULT", key, ""
            
        # For detailed market keys
        if len(parts) >= 2:
            # Joined all but the last part as the market
            # and the last part as the outcome
            market_parts = parts[:-1]
            outcome = parts[-1]
            
            # Try to extract team name if present
            team_name = ""
            for part in market_parts:
                # Check if this part looks like a team name
                if any(team_indicator not in part.lower() for team_indicator in 
                      ["over", "under", "yes", "no", "odd", "even", "1st", "2nd", "half", 
                       "time", "score", "correct", "double", "chance", "total", "asian", 
                       "handicap", "goals", "btts"]):
                    team_name = part
                    break
            
            market_name = "_".join(market_parts)
            return market_name, outcome, team_name
        else:
            # Simple key with one part
            return key, "", ""
    
    def normalize_odds_key(self, key: str) -> str:
        """
        Normalize an odds key to a standard format.
        
        Args:
            key: Raw odds key from a bookmaker
            
        Returns:
            Normalized odds key
        """
        # Check if we've already normalized this key
        if key in self.key_mappings:
            return self.key_mappings[key]
            
        # Parse the key into components
        market_name, outcome, team_name = self.parse_odds_key(key)
        
        # Normalize the market category
        market_category = self.normalize_market_category(market_name)
        
        # Normalize the market name
        normalized_market = self.normalize_market_name(market_name)
        
        # Normalize the outcome
        normalized_outcome = self.normalize_outcome(market_category, outcome)
        
        # Combine into a normalized key
        if team_name:
            normalized_key = f"{normalized_market}_{team_name}_{normalized_outcome}"
        else:
            normalized_key = f"{normalized_market}_{normalized_outcome}"
            
        # Store the mapping
        self.key_mappings[key] = normalized_key
        self.detected_markets.add(normalized_market)
        
        return normalized_key
    
    def normalize_event_odds(self, event_odds: Dict[str, Any]) -> Dict[str, Any]:
        """
        Normalize all odds for a single event.
        
        Args:
            event_odds: Dictionary with event odds data
            
        Returns:
            Dictionary with normalized odds data
        """
        normalized_event = {
            # Keep original identification fields
            "ID": event_odds.get("ID"),
            "Name": event_odds.get("Name"),
            "Home": event_odds.get("Home"),
            "Away": event_odds.get("Away"),
            "Start": event_odds.get("Start"),
            "Country": event_odds.get("Country"),
            "Competition": event_odds.get("Competition"),
            
            # Container for normalized odds
            "normalized_odds": {}
        }
        
        # Process each odds key
        for key, value in event_odds.items():
            # Skip non-odds fields (identification fields)
            if key in ["ID", "Name", "HomeTeamID", "Home", "AwayTeamID", "Away", 
                      "CountryID", "Country", "CompetitionID", "Competition", "Start"]:
                continue
                
            # Skip empty or non-numeric values
            if value in (None, "", "N/A") or (isinstance(value, str) and not value.replace('.', '', 1).isdigit()):
                continue
                
            try:
                # Try to convert to float for consistency
                numeric_value = float(value)
                
                # Normalize the key
                normalized_key = self.normalize_odds_key(key)
                
                # Add to normalized odds
                normalized_event["normalized_odds"][normalized_key] = numeric_value
            except (ValueError, TypeError):
                # Skip if the value can't be converted to a float
                continue
        
        return normalized_event
    
    def normalize_bookmaker_odds(self, bookmaker: str, odds_data: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        Normalize all odds for a bookmaker.
        
        Args:
            bookmaker: Name of the bookmaker
            odds_data: List of odds data dictionaries
            
        Returns:
            List of dictionaries with normalized odds data
        """
        normalized_odds = []
        
        for event_odds in odds_data:
            # Normalize the event odds
            normalized_event = self.normalize_event_odds(event_odds)
            
            # Add bookmaker information
            normalized_event["Bookmaker"] = bookmaker
            
            normalized_odds.append(normalized_event)
            
        return normalized_odds
    
    def get_detected_markets(self) -> List[str]:
        """
        Get a list of all detected market types.
        
        Returns:
            List of normalized market names
        """
        return sorted(list(self.detected_markets))
    
    def extract_common_odds(self, events_by_bookmaker: Dict[str, List[Dict[str, Any]]]) -> Dict[str, Dict[str, Dict[str, float]]]:
        """
        Extract and organize odds by event, bookmaker, and market.
        
        Args:
            events_by_bookmaker: Dictionary with bookmaker name as key and list of events as value
            
        Returns:
            Dictionary with event ID as key and nested dictionaries for bookmakers and markets
        """
        normalized_events = {}
        
        # Process each bookmaker's events
        for bookmaker, events in events_by_bookmaker.items():
            normalized_bookie_events = self.normalize_bookmaker_odds(bookmaker, events)
            
            # Group by event
            for event in normalized_bookie_events:
                event_id = f"{event['ID']}"
                event_name = event['Name']
                
                # Create event entry if it doesn't exist
                if event_id not in normalized_events:
                    normalized_events[event_id] = {
                        "name": event_name,
                        "home": event['Home'],
                        "away": event['Away'],
                        "start": event['Start'],
                        "bookmakers": {}
                    }
                    
                # Add bookmaker's odds
                normalized_events[event_id]["bookmakers"][bookmaker] = event["normalized_odds"]
        
        return normalized_events
    
    def find_best_odds(self, events_odds: Dict[str, Dict[str, Dict[str, float]]]) -> Dict[str, Dict[str, Dict[str, Any]]]:
        """
        Identify the best odds for each market across bookmakers.
        
        Args:
            events_odds: Dictionary with event ID as key and nested dictionaries for bookmakers and markets
            
        Returns:
            Dictionary with event ID as key and nested dictionaries for markets with best odds info
        """
        best_odds_by_event = {}
        
        # Process each event
        for event_id, event_data in events_odds.items():
            best_odds_by_market = {}
            
            # Combine all market types across bookmakers
            all_markets = set()
            for bookmaker, odds in event_data["bookmakers"].items():
                all_markets.update(odds.keys())
                
            # Find best odds for each market
            for market in all_markets:
                best_value = 0
                best_bookmaker = None
                
                for bookmaker, odds in event_data["bookmakers"].items():
                    if market in odds:
                        try:
                            odds_value = float(odds[market])
                            if odds_value > best_value:
                                best_value = odds_value
                                best_bookmaker = bookmaker
                        except (ValueError, TypeError):
                            # Skip if odds value is not a valid number
                            continue
                        
                if best_bookmaker:
                    best_odds_by_market[market] = {
                        "odds": best_value,
                        "bookmaker": best_bookmaker
                    }
                    
            # Store best odds for this event
            best_odds_by_event[event_id] = {
                "name": event_data["name"],
                "home": event_data["home"],
                "away": event_data["away"],
                "start": event_data["start"],
                "markets": best_odds_by_market
            }
            
        return best_odds_by_event