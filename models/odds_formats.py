"""
Module for normalizing odds formats from different bookmakers.

This module provides a base normalizer and bookmaker-specific normalizers
for standardizing odds formats from different sources.
"""

from typing import Dict, Any, List, Tuple, Optional
import re


def safe_float(value: Any) -> Optional[float]:
    """
    Safely convert a value to float, returning None if conversion fails.

    Args:
        value: Value to convert

    Returns:
        Float value or None if conversion fails
    """
    if value is None:
        return None

    try:
        return float(value)
    except (ValueError, TypeError):
        return None


class BaseOddsNormalizer:
    """Base class for normalizing odds formats."""

    # Standard market types
    STANDARD_MARKETS = {
        "1X2": ["1", "X", "2"],
        "DOUBLE_CHANCE": ["1X", "12", "X2"],
        "BTTS": ["YES", "NO"],
        "OVER_UNDER_0.5": ["OVER", "UNDER"],
        "OVER_UNDER_1.5": ["OVER", "UNDER"],
        "OVER_UNDER_2.5": ["OVER", "UNDER"],
        "OVER_UNDER_3.5": ["OVER", "UNDER"],
        "OVER_UNDER_4.5": ["OVER", "UNDER"],
        "FIRST_HALF_RESULT": ["1", "X", "2"],
        "DRAW_NO_BET": ["1", "2"],
        "CORRECT_SCORE": [],  # Various scores like "1-0", "2-1", etc.
        "HALFTIME_FULLTIME": [],  # Various combinations like "1/1", "X/2", etc.
    }

    def __init__(self):
        """Initialize the normalizer."""
        pass

    def normalize_odds(self, raw_odds: Dict[str, Any]) -> Dict[str, Dict[str, float]]:
        """
        Normalize raw odds data into a standardized format.

        Args:
            raw_odds: Raw odds data from the bookmaker

        Returns:
            Dictionary with normalized market structure
        """
        # This should be implemented by each bookmaker-specific normalizer
        raise NotImplementedError("This method must be implemented by subclasses")

    def extract_event_data(self, event_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Extract basic event data (ID, name, teams, etc.) from event data.

        Args:
            event_data: Raw event data from the bookmaker

        Returns:
            Dictionary with standard event data fields
        """
        return {
            "ID": event_data.get("ID"),
            "Name": event_data.get("Name", ""),
            "Home": event_data.get("Home", ""),
            "Away": event_data.get("Away", ""),
            "Start": event_data.get("Start", ""),
            "Country": event_data.get("Country", ""),
            "Competition": event_data.get("Competition", ""),
            "Markets": {}
        }

    def clean_team_name(self, name: str) -> str:
        """
        Clean team name by removing special markers.

        Args:
            name: Raw team name

        Returns:
            Cleaned team name
        """
        # Remove special markers like (W), (F), etc.
        return re.sub(r'\s+\([A-Za-z]+\)', '', name)

    def extract_pattern_markets(self, raw_odds: Dict[str, Any], pattern: str,
                               market_name: str, group_index: int = 1,
                               value_transform = None) -> Dict[str, float]:
        """
        Extract markets matching a pattern from raw odds.

        Args:
            raw_odds: Raw odds data
            pattern: Regex pattern to match
            market_name: Name for the normalized market
            group_index: Index of the regex group to extract
            value_transform: Optional function to transform the value

        Returns:
            Dictionary of extracted markets
        """
        markets = {}
        for key, value in raw_odds.items():
            match = re.search(pattern, key)
            if match:
                odds_value = safe_float(value)
                if odds_value:
                    choice = match.group(group_index)
                    if value_transform:
                        choice = value_transform(choice)
                    markets[choice] = odds_value

        return markets


class BetcenterNormalizer(BaseOddsNormalizer):
    """Normalizer for Betcenter odds format."""

    def normalize_odds(self, raw_odds: Dict[str, Any]) -> Dict[str, Dict[str, float]]:
        """
        Normalize Betcenter odds data into a standardized format.

        Args:
            raw_odds: Raw odds data from Betcenter

        Returns:
            Dictionary with normalized market structure
        """
        normalized_markets = {}

        # Process 1X2 market
        if "1" in raw_odds and "X" in raw_odds and "2" in raw_odds:
            # Convert values to float safely
            home_odds = safe_float(raw_odds["1"])
            draw_odds = safe_float(raw_odds["X"])
            away_odds = safe_float(raw_odds["2"])

            # Only add if all values are valid
            if home_odds and draw_odds and away_odds:
                normalized_markets["1X2"] = {
                    "1": home_odds,
                    "X": draw_odds,
                    "2": away_odds
                }

        # Process Double Chance market
        dc_pattern = r"Double chance_([12X]/[12X])"
        dc_markets = self.extract_pattern_markets(
            raw_odds, dc_pattern, "DOUBLE_CHANCE",
            value_transform=lambda x: x.replace("/", "")
        )

        if dc_markets:
            normalized_markets["DOUBLE_CHANCE"] = dc_markets

        # Process first goal market
        first_goal_pattern = r"Who will score the 1st goal\?_(.+)"

        def transform_first_goal(choice):
            if choice == "1":
                return "1"
            elif choice == "2":
                return "2"
            elif choice == "X":
                return "NO_GOAL"
            else:
                return choice

        first_goal_markets = self.extract_pattern_markets(
            raw_odds, first_goal_pattern, "FIRST_GOAL",
            value_transform=transform_first_goal
        )

        if first_goal_markets:
            normalized_markets["FIRST_GOAL"] = first_goal_markets

        # Process handicap markets
        hc_pattern = r"Who will win the match\? \(HC (\d+):(\d+)\)_(.+)"
        for key, value in raw_odds.items():
            hc_match = re.search(hc_pattern, key)
            if hc_match:
                home_hc = int(hc_match.group(1))
                away_hc = int(hc_match.group(2))
                choice = hc_match.group(3)

                # Calculate the effective handicap
                if home_hc > 0:
                    handicap = f"-{home_hc}"  # Home team starts with -X
                elif away_hc > 0:
                    handicap = f"+{away_hc}"  # Home team starts with +X
                else:
                    handicap = "0"

                market_name = f"HANDICAP_{handicap}"

                if market_name not in normalized_markets:
                    normalized_markets[market_name] = {}

                normalized_markets[market_name][choice] = float(value)

        # Process Over/Under markets
        ou_pattern = r"Over \+ / Under - (\d+\.?\d*) goals(?:\s+\((.+)\))?_(.+)"
        for key, value in raw_odds.items():
            ou_match = re.search(ou_pattern, key)
            if ou_match:
                threshold = ou_match.group(1)
                period = ou_match.group(2)  # This might be None if not specified
                choice = ou_match.group(3)

                # Map to OVER or UNDER
                if choice.endswith("+") or "+" in choice:
                    normalized_choice = "OVER"
                elif choice.startswith("0-") or "-" in choice:
                    normalized_choice = "UNDER"
                else:
                    normalized_choice = choice

                # Determine market name based on period
                if period and ("1st half" in period.lower() or "first half" in period.lower()):
                    market_name = f"OVER_UNDER_{threshold}_FIRST_HALF"
                else:
                    market_name = f"OVER_UNDER_{threshold}"

                if market_name not in normalized_markets:
                    normalized_markets[market_name] = {}

                normalized_markets[market_name][normalized_choice] = float(value)

        # Process first half result
        half_result_pattern = r"Who will win the (?:1st|first) half\?_(.+)"
        half_markets = {}
        for key, value in raw_odds.items():
            half_match = re.search(half_result_pattern, key)
            if half_match:
                choice = half_match.group(1)
                half_markets[choice] = float(value)

        if len(half_markets) > 0:
            normalized_markets["FIRST_HALF_RESULT"] = half_markets

        # Process correct score markets
        cs_pattern = r"Correct score(?: .+)?_(.+)"
        cs_markets = {}
        for key, value in raw_odds.items():
            cs_match = re.search(cs_pattern, key)
            if cs_match:
                score = cs_match.group(1)
                if score and score not in ["", "_"]:
                    # Standardize score format
                    score = score.replace(":", "-")
                    cs_markets[score] = float(value)

        if len(cs_markets) > 0:
            normalized_markets["CORRECT_SCORE"] = cs_markets

        return normalized_markets


class UnibetNormalizer(BaseOddsNormalizer):
    """Normalizer for Unibet odds format."""

    def normalize_odds(self, raw_odds: Dict[str, Any]) -> Dict[str, Dict[str, float]]:
        """
        Normalize Unibet odds data into a standardized format.

        Args:
            raw_odds: Raw odds data from Unibet

        Returns:
            Dictionary with normalized market structure
        """
        normalized_markets = {}

        # Process 1X2 market
        if "1" in raw_odds and "X" in raw_odds and "2" in raw_odds:
            # Convert values to float safely
            home_odds = safe_float(raw_odds["1"])
            draw_odds = safe_float(raw_odds["X"])
            away_odds = safe_float(raw_odds["2"])

            # Only add if all values are valid
            if home_odds and draw_odds and away_odds:
                normalized_markets["1X2"] = {
                    "1": home_odds,
                    "X": draw_odds,
                    "2": away_odds
                }

        # Process Match result (same as 1X2 but with different naming)
        match_pattern = r"Match_Full Time_([12X])"
        match_markets = {}
        for key, value in raw_odds.items():
            match_result = re.search(match_pattern, key)
            if match_result:
                choice = match_result.group(1)
                match_markets[choice] = float(value)

        if len(match_markets) == 3:  # Only if we have all 3 outcomes
            normalized_markets["1X2"] = match_markets

        # Process BTTS (Both Teams To Score)
        btts_pattern = r"Yes/No_Both Teams To Score_(Yes|No)"
        btts_markets = {}
        for key, value in raw_odds.items():
            btts_match = re.search(btts_pattern, key)
            if btts_match:
                choice = btts_match.group(1).upper()
                btts_markets[choice] = float(value)

        if len(btts_markets) > 0:
            normalized_markets["BTTS"] = btts_markets

        # Process Odd/Even
        odd_even_pattern = r"Odd/Even_.+_(Even|Odd)"
        odd_even_markets = {}
        for key, value in raw_odds.items():
            odd_even_match = re.search(odd_even_pattern, key)
            if odd_even_match:
                choice = odd_even_match.group(1).upper()
                odd_even_markets[choice] = float(value)

        if len(odd_even_markets) > 0:
            normalized_markets["ODD_EVEN"] = odd_even_markets

        # Process Over/Under markets
        ou_pattern = r"Over/Under_(?:.+?)_(?:Over|Under)_(\d+\.?\d*)"
        for key, value in raw_odds.items():
            ou_match = re.search(ou_pattern, key)
            if ou_match:
                threshold = ou_match.group(1)
                # Determine if it's over or under
                if "Over" in key:
                    choice = "OVER"
                elif "Under" in key:
                    choice = "UNDER"
                else:
                    continue

                # Check if it's first half
                if "1st Half" in key or "First Half" in key:
                    market_name = f"OVER_UNDER_{threshold}_FIRST_HALF"
                # Check if it's team-specific
                elif any(team in key for team in ["Home", "Away", "by"]):
                    # For simplicity, just use a general team goals market
                    market_name = f"TEAM_OVER_UNDER_{threshold}"
                else:
                    market_name = f"OVER_UNDER_{threshold}"

                if market_name not in normalized_markets:
                    normalized_markets[market_name] = {}

                normalized_markets[market_name][choice] = float(value)

        # Process Draw No Bet
        dnb_pattern = r"Match_Draw No Bet_([12])"
        dnb_markets = {}
        for key, value in raw_odds.items():
            dnb_match = re.search(dnb_pattern, key)
            if dnb_match:
                choice = dnb_match.group(1)
                dnb_markets[choice] = float(value)

        if len(dnb_markets) > 0:
            normalized_markets["DRAW_NO_BET"] = dnb_markets

        # Process Half Time/Full Time
        htft_pattern = r"HT/FT_Half Time/Full Time_(.+)"
        htft_markets = {}
        for key, value in raw_odds.items():
            htft_match = re.search(htft_pattern, key)
            if htft_match:
                choice = htft_match.group(1)
                # Standardize format (e.g., "1/2" instead of "1-2")
                choice = choice.replace("-", "/")
                htft_markets[choice] = float(value)

        if len(htft_markets) > 0:
            normalized_markets["HALFTIME_FULLTIME"] = htft_markets

        # Process Correct Score
        cs_pattern = r"Correct Score_Correct Score_(.+)"
        cs_markets = {}
        for key, value in raw_odds.items():
            cs_match = re.search(cs_pattern, key)
            if cs_match:
                score = cs_match.group(1)
                # Standardize score format
                score = score.replace(":", "-")
                cs_markets[score] = float(value)

        if len(cs_markets) > 0:
            normalized_markets["CORRECT_SCORE"] = cs_markets

        # Process Double Chance
        dc_pattern = r"Double Chance_Double Chance_(1X|12|X2)"
        dc_markets = {}
        for key, value in raw_odds.items():
            dc_match = re.search(dc_pattern, key)
            if dc_match:
                choice = dc_match.group(1)
                dc_markets[choice] = float(value)

        if len(dc_markets) > 0:
            normalized_markets["DOUBLE_CHANCE"] = dc_markets

        # Process Half Time
        ht_pattern = r"Match_Half Time_([12X])"
        ht_markets = {}
        for key, value in raw_odds.items():
            ht_match = re.search(ht_pattern, key)
            if ht_match:
                choice = ht_match.group(1)
                ht_markets[choice] = float(value)

        if len(ht_markets) > 0:
            normalized_markets["FIRST_HALF_RESULT"] = ht_markets

        return normalized_markets


# Import the PinnacleNormalizer
from models.pinnacle_normalizer import PinnacleNormalizer

# Factory function to get the right normalizer for a bookmaker
def get_normalizer(bookmaker: str) -> BaseOddsNormalizer:
    """
    Get the appropriate normalizer for a bookmaker.

    Args:
        bookmaker: Name of the bookmaker

    Returns:
        Normalizer instance for the specified bookmaker
    """
    normalizers = {
        "betcenter": BetcenterNormalizer(),
        "unibet": UnibetNormalizer(),
        "pinnacle": PinnacleNormalizer(),
        # Add more normalizers as they are implemented
    }

    # Default to base normalizer if specific one not found
    return normalizers.get(bookmaker.lower(), BaseOddsNormalizer())