"""
Module for normalizing Pinnacle odds data.

This module provides a normalizer for Pinnacle odds data to convert it
to a standardized format consistent with other bookmakers.
"""

import re
from typing import Dict, Any, List, Optional
from models.odds_formats import BaseOddsNormalizer, safe_float


class PinnacleNormalizer(BaseOddsNormalizer):
    """Normalizer for Pinnacle odds format."""

    def normalize_odds(self, raw_odds: Dict[str, Any]) -> Dict[str, Dict[str, float]]:
        """
        Normalize Pinnacle odds data into a standardized format.
        
        Args:
            raw_odds: Raw odds data from Pinnacle
            
        Returns:
            Dictionary with normalized market structure
        """
        normalized_markets = {}
        
        # Process 1X2 market
        if "1" in raw_odds and "X" in raw_odds and "2" in raw_odds:
            # Convert values to float safely
            home_odds = safe_float(raw_odds["1"])
            draw_odds = safe_float(raw_odds["X"])
            away_odds = safe_float(raw_odds["2"])
            
            # Only add if all values are valid
            if home_odds and draw_odds and away_odds:
                normalized_markets["1X2"] = {
                    "1": home_odds,
                    "X": draw_odds,
                    "2": away_odds
                }
        
        # Process Match result (same as 1X2 but with different naming)
        match_pattern = r"Match_Full Time_([12X])"
        match_markets = {}
        for key, value in raw_odds.items():
            match_result = re.search(match_pattern, key)
            if match_result:
                choice = match_result.group(1)
                match_markets[choice] = float(value)
        
        if len(match_markets) == 3 and "1X2" not in normalized_markets:  # Only if we have all 3 outcomes and haven't added 1X2 yet
            normalized_markets["1X2"] = match_markets
        
        # Process Double Chance
        dc_pattern = r"Double Chance_Double Chance_(1X|12|X2)"
        dc_markets = {}
        for key, value in raw_odds.items():
            dc_match = re.search(dc_pattern, key)
            if dc_match:
                choice = dc_match.group(1)
                dc_markets[choice] = float(value)
        
        if len(dc_markets) > 0:
            normalized_markets["DOUBLE_CHANCE"] = dc_markets
        
        # Process Over/Under markets
        ou_pattern = r"Over/Under_Total Goals(?:_(?:Full Time|1st Half|2nd Half))?(?:_(\d+\.?\d*))?_(Over|Under)"
        for key, value in raw_odds.items():
            ou_match = re.search(ou_pattern, key)
            if ou_match:
                threshold = ou_match.group(1)
                choice = ou_match.group(2).upper()
                
                # Check if it's first half
                if "1st Half" in key:
                    market_name = f"OVER_UNDER_{threshold}_FIRST_HALF"
                elif "2nd Half" in key:
                    market_name = f"OVER_UNDER_{threshold}_SECOND_HALF"
                else:
                    market_name = f"OVER_UNDER_{threshold}"
                
                if market_name not in normalized_markets:
                    normalized_markets[market_name] = {}
                
                normalized_markets[market_name][choice] = float(value)
        
        # Process Team Total markets
        team_total_pattern = r"Over/Under_Total Goals by (home|away)(?:_(?:Full Time|1st Half|2nd Half))?(?:_(\d+\.?\d*))?_(Over|Under)"
        for key, value in raw_odds.items():
            team_match = re.search(team_total_pattern, key, re.IGNORECASE)
            if team_match:
                team = team_match.group(1).upper()
                threshold = team_match.group(2)
                choice = team_match.group(3).upper()
                
                # Check if it's first half
                if "1st Half" in key:
                    market_name = f"{team}_OVER_UNDER_{threshold}_FIRST_HALF"
                elif "2nd Half" in key:
                    market_name = f"{team}_OVER_UNDER_{threshold}_SECOND_HALF"
                else:
                    market_name = f"{team}_OVER_UNDER_{threshold}"
                
                if market_name not in normalized_markets:
                    normalized_markets[market_name] = {}
                
                normalized_markets[market_name][choice] = float(value)
        
        # Process Asian Handicap markets
        ah_pattern = r"Asian Handicap(?:_(?:Full Time|1st Half|2nd Half))?_(.+?)_([+-]?\d+\.?\d*)"
        for key, value in raw_odds.items():
            ah_match = re.search(ah_pattern, key)
            if ah_match:
                team = ah_match.group(1)
                handicap = ah_match.group(2)
                
                # Determine if it's home or away team
                if team.lower() == raw_odds.get("Home", "").lower():
                    choice = "1"
                else:
                    choice = "2"
                
                # Check if it's first half
                if "1st Half" in key:
                    market_name = f"ASIAN_HANDICAP_{handicap}_FIRST_HALF"
                elif "2nd Half" in key:
                    market_name = f"ASIAN_HANDICAP_{handicap}_SECOND_HALF"
                else:
                    market_name = f"ASIAN_HANDICAP_{handicap}"
                
                if market_name not in normalized_markets:
                    normalized_markets[market_name] = {}
                
                normalized_markets[market_name][choice] = float(value)
        
        # Process First Half Result
        half_result_pattern = r"Match_1st Half_([12X])"
        half_markets = {}
        for key, value in raw_odds.items():
            half_match = re.search(half_result_pattern, key)
            if half_match:
                choice = half_match.group(1)
                half_markets[choice] = float(value)
        
        if len(half_markets) > 0:
            normalized_markets["FIRST_HALF_RESULT"] = half_markets
        
        # Process Draw No Bet
        dnb_pattern = r"Match_Draw No Bet_([12])"
        dnb_markets = {}
        for key, value in raw_odds.items():
            dnb_match = re.search(dnb_pattern, key)
            if dnb_match:
                choice = dnb_match.group(1)
                dnb_markets[choice] = float(value)
        
        if len(dnb_markets) > 0:
            normalized_markets["DRAW_NO_BET"] = dnb_markets
        
        return normalized_markets
