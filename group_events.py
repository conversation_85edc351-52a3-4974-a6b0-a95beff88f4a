import json
import os
from datetime import datetime, timedelta
import re
from difflib import SequenceMatcher
from collections import defaultdict

def normalize_team_name(name):
    """Normalize team names for better matching"""
    if not name:
        return ""

    # Convert to lowercase
    name = name.lower().strip()

    # Remove common prefixes/suffixes
    prefixes = ['fc', 'cf', 'ac', 'sc', 'cd', 'ca', 'club', 'real', 'atletico', 'sporting', 'sv', 'ad', 'cs']
    suffixes = ['fc', 'cf', 'ac', 'sc', 'cd', 'ca', 'united', 'city', 'town', 'rovers', 'wanderers']

    # Remove brackets and content within them
    name = re.sub(r'\([^)]*\)', '', name)

    # Remove common abbreviations and suffixes
    for prefix in prefixes:
        if name.startswith(prefix + ' '):
            name = name[len(prefix):].strip()

    for suffix in suffixes:
        if name.endswith(' ' + suffix):
            name = name[:-len(suffix)].strip()

    # Remove special characters and extra spaces
    name = re.sub(r'[^\w\s]', ' ', name)
    name = re.sub(r'\s+', ' ', name).strip()

    # Handle common abbreviations
    abbreviations = {
        'utd': 'united',
        'int': 'internacional',
        'atl': 'atletico',
        'sp': 'sao paulo',
        'rj': 'rio de janeiro',
        'mg': 'minas gerais',
        'rs': 'rio grande do sul',
        'ce': 'ceara',
        'ba': 'bahia',
        'pr': 'parana',
        'sc': 'santa catarina',
        'go': 'goias',
        'df': 'distrito federal',
        'u20': 'under 20',
        'u21': 'under 21',
        'u23': 'under 23',
        'u19': 'under 19',
        'w': 'women'
    }

    words = name.split()
    normalized_words = []
    for word in words:
        if word in abbreviations:
            normalized_words.append(abbreviations[word])
        else:
            normalized_words.append(word)

    return ' '.join(normalized_words)

def similarity_score(name1, name2):
    """Calculate similarity score between two team names"""
    norm1 = normalize_team_name(name1)
    norm2 = normalize_team_name(name2)

    if norm1 == norm2:
        return 1.0

    # Use sequence matcher for similarity
    return SequenceMatcher(None, norm1, norm2).ratio()

def parse_datetime(date_str):
    """Parse datetime string from different formats"""
    if not date_str:
        return None

    # Remove timezone info for comparison
    date_str = date_str.replace('Z', '').replace('+00:00', '')

    formats = [
        '%Y-%m-%dT%H:%M:%S.%f',
        '%Y-%m-%dT%H:%M:%S',
        '%Y-%m-%d %H:%M:%S',
        '%Y-%m-%dT%H:%M:%S.%fZ',
        '%Y-%m-%dT%H:%M:%SZ'
    ]

    for fmt in formats:
        try:
            return datetime.strptime(date_str, fmt)
        except ValueError:
            continue

    return None

def events_match(event1, event2, time_tolerance_hours=2):
    """Check if two events are the same match"""
    # Parse start times
    time1 = parse_datetime(event1.get('Start'))
    time2 = parse_datetime(event2.get('Start'))

    # If we can't parse times, skip time check
    time_match = True
    if time1 and time2:
        time_diff = abs((time1 - time2).total_seconds() / 3600)
        time_match = time_diff <= time_tolerance_hours

    if not time_match:
        return False

    # Check team name similarity
    home1, away1 = event1.get('Home', ''), event1.get('Away', '')
    home2, away2 = event2.get('Home', ''), event2.get('Away', '')

    # Calculate similarity scores
    home_sim = similarity_score(home1, home2)
    away_sim = similarity_score(away1, away2)

    # Also check if teams are swapped (home/away reversed)
    home_away_sim = similarity_score(home1, away2)
    away_home_sim = similarity_score(away1, home2)

    # Consider it a match if both teams have high similarity
    direct_match = home_sim >= 0.8 and away_sim >= 0.8
    swapped_match = home_away_sim >= 0.8 and away_home_sim >= 0.8

    return direct_match or swapped_match

def load_events_from_file(filepath):
    """Load events from a JSON file"""
    try:
        with open(filepath, 'r', encoding='utf-8') as f:
            events = json.load(f)
            return events if isinstance(events, list) else []
    except (FileNotFoundError, json.JSONDecodeError) as e:
        print(f"Error loading {filepath}: {e}")
        return []

def main():
    # Define bookmaker files
    bookmaker_files = {
        'bet777': 'output/bet777/football/all_football_events.json',
        'betcenter': 'output/betcenter/football/all_football_events.json',
        'betfirst': 'output/betfirst/football/all_football_events.json',
        'napoleon': 'output/napoleon/football/all_football_events.json',
        'pinnacle': 'output/pinnacle/football/all_football_events.json',
        'unibet': 'output/unibet/football/all_football_events.json'
    }

    # Load all events
    all_events = {}
    for bookmaker, filepath in bookmaker_files.items():
        events = load_events_from_file(filepath)
        all_events[bookmaker] = events
        print(f"Loaded {len(events)} events from {bookmaker}")

    # Group events
    grouped_events = []
    processed_events = set()

    # Start with the bookmaker that has the most events (usually pinnacle or betcenter)
    primary_bookmaker = max(all_events.keys(), key=lambda k: len(all_events[k]))
    print(f"Using {primary_bookmaker} as primary bookmaker")

    for primary_event in all_events[primary_bookmaker]:
        primary_key = f"{primary_bookmaker}_{primary_event.get('ID')}"

        if primary_key in processed_events:
            continue

        # Create a group for this event
        event_group = {
            'event_name': primary_event.get('Name', ''),
            'home_team': primary_event.get('Home', ''),
            'away_team': primary_event.get('Away', ''),
            'start_time': primary_event.get('Start', ''),
            'country': primary_event.get('Country', ''),
            'competition': primary_event.get('Competition', ''),
            'bookmakers': {
                primary_bookmaker: primary_event
            }
        }

        processed_events.add(primary_key)

        # Look for matching events in other bookmakers
        for other_bookmaker, other_events in all_events.items():
            if other_bookmaker == primary_bookmaker:
                continue

            for other_event in other_events:
                other_key = f"{other_bookmaker}_{other_event.get('ID')}"

                if other_key in processed_events:
                    continue

                if events_match(primary_event, other_event):
                    event_group['bookmakers'][other_bookmaker] = other_event
                    processed_events.add(other_key)

        grouped_events.append(event_group)

    # Handle remaining events from other bookmakers that didn't match
    for bookmaker, events in all_events.items():
        if bookmaker == primary_bookmaker:
            continue

        for event in events:
            event_key = f"{bookmaker}_{event.get('ID')}"

            if event_key not in processed_events:
                event_group = {
                    'event_name': event.get('Name', ''),
                    'home_team': event.get('Home', ''),
                    'away_team': event.get('Away', ''),
                    'start_time': event.get('Start', ''),
                    'country': event.get('Country', ''),
                    'competition': event.get('Competition', ''),
                    'bookmakers': {
                        bookmaker: event
                    }
                }
                grouped_events.append(event_group)
                processed_events.add(event_key)

    # Sort by start time
    def get_start_time(group):
        start_time = group.get('start_time', '')
        parsed_time = parse_datetime(start_time)
        return parsed_time if parsed_time else datetime.min

    grouped_events.sort(key=get_start_time)

    # Generate statistics
    total_events = sum(len(events) for events in all_events.values())
    matched_events = sum(1 for group in grouped_events if len(group['bookmakers']) > 1)
    unique_events = len(grouped_events)

    print(f"\n=== GROUPING RESULTS ===")
    print(f"Total events across all bookmakers: {total_events}")
    print(f"Unique events identified: {unique_events}")
    print(f"Events found in multiple bookmakers: {matched_events}")
    print(f"Events found in only one bookmaker: {unique_events - matched_events}")

    # Show distribution of events by number of bookmakers
    bookmaker_distribution = defaultdict(int)
    for group in grouped_events:
        num_bookmakers = len(group['bookmakers'])
        bookmaker_distribution[num_bookmakers] += 1

    print(f"\n=== DISTRIBUTION BY NUMBER OF BOOKMAKERS ===")
    for num_bookmakers in sorted(bookmaker_distribution.keys(), reverse=True):
        count = bookmaker_distribution[num_bookmakers]
        print(f"Events in {num_bookmakers} bookmaker(s): {count}")

    # Save grouped events
    output_file = 'output/grouped_events.json'
    with open(output_file, 'w', encoding='utf-8') as f:
        json.dump(grouped_events, f, indent=2, ensure_ascii=False)

    print(f"\nGrouped events saved to: {output_file}")

    # Show some examples of matched events
    print(f"\n=== EXAMPLES OF MATCHED EVENTS ===")
    examples_shown = 0
    for group in grouped_events:
        if len(group['bookmakers']) > 2 and examples_shown < 5:
            print(f"\nEvent: {group['event_name']}")
            print(f"Start: {group['start_time']}")
            print(f"Found in {len(group['bookmakers'])} bookmakers:")
            for bookmaker in group['bookmakers']:
                print(f"  - {bookmaker}")
            examples_shown += 1

if __name__ == "__main__":
    main()
